/**
 * Frontend Monitoring and Metrics Collection
 * Collects custom metrics for Grafana monitoring in QA environment
 */

export interface MetricData {
  name: string;
  value: number;
  timestamp: number;
  labels?: Record<string, string>;
  environment?: string;
}

export interface ErrorMetric {
  error: string;
  stack?: string;
  url: string;
  userAgent: string;
  timestamp: number;
  userId?: string;
  sessionId: string;
  environment: string;
}

export interface PerformanceMetric {
  name: string;
  value: number;
  timestamp: number;
  url: string;
  environment: string;
}

export interface UserInteractionMetric {
  action: string;
  component: string;
  url: string;
  timestamp: number;
  userId?: string;
  sessionId: string;
  environment: string;
  metadata?: Record<string, any>;
}

class MetricsCollector {
  private endpoint: string;
  private environment: string;
  private sessionId: string;
  private userId?: string;
  private buffer: MetricData[] = [];
  private errorBuffer: ErrorMetric[] = [];
  private performanceBuffer: PerformanceMetric[] = [];
  private interactionBuffer: UserInteractionMetric[] = [];
  private flushInterval: NodeJS.Timeout | null = null;

  constructor() {
    this.environment = process.env.NEXT_PUBLIC_NODE_ENV || 'development';
    this.endpoint = process.env.NEXT_PUBLIC_METRICS_ENDPOINT || '';
    this.sessionId = this.generateSessionId();
    
    // Auto-flush metrics every 30 seconds
    this.startAutoFlush();
    
    // Setup performance observer
    this.setupPerformanceObserver();
    
    // Setup error tracking
    this.setupErrorTracking();
  }

  private generateSessionId(): string {
    return `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }

  setUserId(userId: string) {
    this.userId = userId;
  }

  // Custom business metrics
  recordMetric(name: string, value: number, labels?: Record<string, string>) {
    const metric: MetricData = {
      name,
      value,
      timestamp: Date.now(),
      labels: {
        ...labels,
        environment: this.environment,
        sessionId: this.sessionId,
        ...(this.userId && { userId: this.userId })
      },
      environment: this.environment
    };
    
    this.buffer.push(metric);
    
    // Flush immediately for critical metrics
    if (name.includes('error') || name.includes('critical')) {
      this.flush();
    }
  }

  // API call metrics
  recordApiCall(endpoint: string, method: string, duration: number, status: number) {
    this.recordMetric('api_call_duration', duration, {
      endpoint,
      method,
      status: status.toString(),
      success: status < 400 ? 'true' : 'false'
    });

    this.recordMetric('api_call_count', 1, {
      endpoint,
      method,
      status: status.toString()
    });
  }

  // User interaction tracking
  recordUserInteraction(action: string, component: string, metadata?: Record<string, any>) {
    const interaction: UserInteractionMetric = {
      action,
      component,
      url: window.location.pathname,
      timestamp: Date.now(),
      userId: this.userId,
      sessionId: this.sessionId,
      environment: this.environment,
      metadata
    };
    
    this.interactionBuffer.push(interaction);
  }

  // Error tracking
  recordError(error: Error | string, context?: Record<string, any>) {
    const errorMetric: ErrorMetric = {
      error: error instanceof Error ? error.message : error,
      stack: error instanceof Error ? error.stack : undefined,
      url: window.location.href,
      userAgent: navigator.userAgent,
      timestamp: Date.now(),
      userId: this.userId,
      sessionId: this.sessionId,
      environment: this.environment
    };
    
    this.errorBuffer.push(errorMetric);
    
    // Also record as a metric for counting
    this.recordMetric('frontend_error_count', 1, {
      error_type: error instanceof Error ? error.name : 'string_error',
      url: window.location.pathname,
      ...context
    });
    
    // Flush errors immediately
    this.flushErrors();
  }

  // Performance metrics
  private setupPerformanceObserver() {
    if (typeof window === 'undefined') return;

    // Page load metrics
    window.addEventListener('load', () => {
      setTimeout(() => {
        const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
        if (navigation) {
          this.recordPerformanceMetric('page_load_time', navigation.loadEventEnd - navigation.fetchStart);
          this.recordPerformanceMetric('dom_content_loaded', navigation.domContentLoadedEventEnd - navigation.fetchStart);
          this.recordPerformanceMetric('first_contentful_paint', navigation.loadEventEnd - navigation.fetchStart);
        }
      }, 0);
    });

    // Core Web Vitals
    if ('PerformanceObserver' in window) {
      // Largest Contentful Paint
      const lcpObserver = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        const lastEntry = entries[entries.length - 1];
        this.recordPerformanceMetric('largest_contentful_paint', lastEntry.startTime);
      });
      lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] });

      // First Input Delay
      const fidObserver = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        entries.forEach((entry: any) => {
          this.recordPerformanceMetric('first_input_delay', entry.processingStart - entry.startTime);
        });
      });
      fidObserver.observe({ entryTypes: ['first-input'] });

      // Cumulative Layout Shift
      let clsValue = 0;
      const clsObserver = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        entries.forEach((entry: any) => {
          if (!entry.hadRecentInput) {
            clsValue += entry.value;
          }
        });
        this.recordPerformanceMetric('cumulative_layout_shift', clsValue);
      });
      clsObserver.observe({ entryTypes: ['layout-shift'] });
    }
  }

  private recordPerformanceMetric(name: string, value: number) {
    const metric: PerformanceMetric = {
      name,
      value,
      timestamp: Date.now(),
      url: window.location.pathname,
      environment: this.environment
    };
    
    this.performanceBuffer.push(metric);
  }

  // Error tracking setup
  private setupErrorTracking() {
    if (typeof window === 'undefined') return;

    // Global error handler
    window.addEventListener('error', (event) => {
      this.recordError(event.error || event.message);
    });

    // Unhandled promise rejection
    window.addEventListener('unhandledrejection', (event) => {
      this.recordError(event.reason);
    });
  }

  // Auto-flush setup
  private startAutoFlush() {
    this.flushInterval = setInterval(() => {
      this.flush();
    }, 30000); // Flush every 30 seconds
  }

  // Flush all metrics
  async flush() {
    if (this.buffer.length === 0 && this.performanceBuffer.length === 0 && this.interactionBuffer.length === 0) {
      return;
    }

    try {
      const payload = {
        metrics: [...this.buffer],
        performance: [...this.performanceBuffer],
        interactions: [...this.interactionBuffer],
        timestamp: Date.now(),
        environment: this.environment,
        sessionId: this.sessionId,
        userId: this.userId,
        url: window.location.href
      };

      // Clear buffers
      this.buffer = [];
      this.performanceBuffer = [];
      this.interactionBuffer = [];

      // Send to metrics endpoint (we'll implement this next)
      await this.sendMetrics(payload);
    } catch (error) {
      console.error('Failed to flush metrics:', error);
    }
  }

  // Flush errors separately for immediate delivery
  async flushErrors() {
    if (this.errorBuffer.length === 0) return;

    try {
      const payload = {
        errors: [...this.errorBuffer],
        timestamp: Date.now(),
        environment: this.environment,
        sessionId: this.sessionId,
        userId: this.userId
      };

      this.errorBuffer = [];
      await this.sendErrors(payload);
    } catch (error) {
      console.error('Failed to flush errors:', error);
    }
  }

  private async sendMetrics(payload: any) {
    if (!this.endpoint) {
      // In development, just log to console
      if (this.environment === 'development') {
        console.log('Metrics:', payload);
      }
      return;
    }

    try {
      await fetch(`${this.endpoint}/metrics`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload)
      });
    } catch (error) {
      console.error('Failed to send metrics:', error);
    }
  }

  private async sendErrors(payload: any) {
    if (!this.endpoint) {
      if (this.environment === 'development') {
        console.error('Error metrics:', payload);
      }
      return;
    }

    try {
      await fetch(`${this.endpoint}/errors`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload)
      });
    } catch (error) {
      console.error('Failed to send error metrics:', error);
    }
  }

  // Cleanup
  destroy() {
    if (this.flushInterval) {
      clearInterval(this.flushInterval);
    }
    this.flush(); // Final flush
  }
}

// Singleton instance
export const metricsCollector = new MetricsCollector();

// Convenience functions
export const recordMetric = (name: string, value: number, labels?: Record<string, string>) => {
  metricsCollector.recordMetric(name, value, labels);
};

export const recordApiCall = (endpoint: string, method: string, duration: number, status: number) => {
  metricsCollector.recordApiCall(endpoint, method, duration, status);
};

export const recordUserInteraction = (action: string, component: string, metadata?: Record<string, any>) => {
  metricsCollector.recordUserInteraction(action, component, metadata);
};

export const recordError = (error: Error | string, context?: Record<string, any>) => {
  metricsCollector.recordError(error, context);
};

export const setUserId = (userId: string) => {
  metricsCollector.setUserId(userId);
};
