{"dashboard": {"id": null, "title": "ARCA EMR - Business Metrics Dashboard", "tags": ["arca-emr", "business", "medical", "qa"], "timezone": "browser", "refresh": "1m", "time": {"from": "now-24h", "to": "now"}, "panels": [{"id": 1, "title": "Patient Management Overview", "type": "stat", "targets": [{"expr": "sum(increase(arca_emr_patient_view[1h]))", "legendFormat": "Patient Views (1h)"}, {"expr": "sum(increase(arca_emr_patient_registration[1h]))", "legendFormat": "New Registrations (1h)"}, {"expr": "sum(increase(arca_emr_patient_search[1h]))", "legendFormat": "Patient Searches (1h)"}], "gridPos": {"h": 6, "w": 24, "x": 0, "y": 0}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "unit": "short"}}}, {"id": 2, "title": "Consultation Activity", "type": "timeseries", "targets": [{"expr": "sum(rate(arca_emr_consultation_start[5m])) by (consultation_type)", "legendFormat": "Started: {{consultation_type}}"}, {"expr": "sum(rate(arca_emr_consultation_complete[5m]))", "legendFormat": "Completed"}], "gridPos": {"h": 8, "w": 12, "x": 0, "y": 6}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "unit": "reqps"}}}, {"id": 3, "title": "Average Consultation Duration", "type": "stat", "targets": [{"expr": "avg(arca_emr_consultation_duration) / 1000 / 60", "legendFormat": "Avg Duration (minutes)"}], "gridPos": {"h": 8, "w": 12, "x": 12, "y": 6}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "unit": "m", "thresholds": {"steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 15}, {"color": "red", "value": 30}]}}}}, {"id": 4, "title": "Prescription Activity", "type": "timeseries", "targets": [{"expr": "sum(rate(arca_emr_prescription_create[5m]))", "legendFormat": "Prescriptions Created"}, {"expr": "sum(rate(arca_emr_prescription_print[5m])) by (print_type)", "legendFormat": "Printed: {{print_type}}"}], "gridPos": {"h": 8, "w": 12, "x": 0, "y": 14}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "unit": "reqps"}}}, {"id": 5, "title": "Lab Test Orders", "type": "timeseries", "targets": [{"expr": "sum(rate(arca_emr_lab_test_order[5m]))", "legendFormat": "Lab Orders"}, {"expr": "sum(rate(arca_emr_lab_result_view[5m])) by (result_type)", "legendFormat": "Results Viewed: {{result_type}}"}], "gridPos": {"h": 8, "w": 12, "x": 12, "y": 14}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "unit": "reqps"}}}, {"id": 6, "title": "Lifestyle Monitoring Usage", "type": "timeseries", "targets": [{"expr": "sum(rate(arca_emr_lifestyle_form_submit[5m])) by (form_type)", "legendFormat": "{{form_type}}"}, {"expr": "sum(rate(arca_emr_nutrition_data_entry[5m])) by (data_type)", "legendFormat": "Nutrition: {{data_type}}"}], "gridPos": {"h": 8, "w": 12, "x": 0, "y": 22}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "unit": "reqps"}}}, {"id": 7, "title": "Ambient Listening Sessions", "type": "timeseries", "targets": [{"expr": "sum(rate(arca_emr_ambient_listening_start[5m])) by (microphone_type)", "legendFormat": "Started: {{microphone_type}}"}, {"expr": "sum(rate(arca_emr_ambient_listening_stop[5m]))", "legendFormat": "Completed"}], "gridPos": {"h": 8, "w": 12, "x": 12, "y": 22}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "unit": "reqps"}}}, {"id": 8, "title": "Top Patient Actions (Last Hour)", "type": "table", "targets": [{"expr": "topk(10, sum(increase(arca_emr_patient_action[1h])) by (action))", "format": "table", "instant": true}], "gridPos": {"h": 8, "w": 12, "x": 0, "y": 30}, "transformations": [{"id": "organize", "options": {"excludeByName": {"Time": true}, "renameByName": {"action": "Action", "Value": "Count"}}}]}, {"id": 9, "title": "User Session Activity", "type": "timeseries", "targets": [{"expr": "sum(rate(arca_emr_user_login[5m]))", "legendFormat": "<PERSON><PERSON>"}, {"expr": "sum(rate(arca_emr_user_logout[5m]))", "legendFormat": "Logouts"}, {"expr": "count(count by (session_id)(arca_emr_page_view))", "legendFormat": "Active Sessions"}], "gridPos": {"h": 8, "w": 12, "x": 12, "y": 30}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "unit": "short"}}}, {"id": 10, "title": "Print Activity by Document Type", "type": "piechart", "targets": [{"expr": "sum(increase(arca_emr_print_action[1h])) by (document_type)", "legendFormat": "{{document_type}}"}], "gridPos": {"h": 8, "w": 12, "x": 0, "y": 38}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "unit": "short"}}}, {"id": 11, "title": "Dashboard Usage Patterns", "type": "timeseries", "targets": [{"expr": "sum(rate(arca_emr_dashboard_view[5m])) by (dashboard_type)", "legendFormat": "{{dashboard_type}}"}, {"expr": "sum(rate(arca_emr_widget_interaction[5m])) by (action)", "legendFormat": "Widget: {{action}}"}], "gridPos": {"h": 8, "w": 12, "x": 12, "y": 38}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "unit": "reqps"}}}]}}