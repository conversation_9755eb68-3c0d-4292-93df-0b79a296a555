/**
 * OpenTelemetry Integration for Grafana Cloud
 * Handles traces, spans, and metrics for Grafana Cloud
 */

import { trace, context, SpanStatusCode, SpanKind } from '@opentelemetry/api';

interface TraceData {
  traceId: string;
  spanId: string;
  parentSpanId?: string;
  operationName: string;
  startTime: number;
  endTime: number;
  duration: number;
  status: 'ok' | 'error';
  tags: Record<string, string>;
  logs?: Array<{
    timestamp: number;
    fields: Record<string, any>;
  }>;
}

class GrafanaOTelCollector {
  private traces: TraceData[] = [];
  private serviceName: string;
  private environment: string;

  constructor() {
    this.serviceName = 'arca-emr-qa';
    this.environment = process.env.NEXT_PUBLIC_NODE_ENV || 'development';
  }

  // Create and start a new span
  startSpan(operationName: string, parentContext?: any) {
    const tracer = trace.getTracer(this.serviceName);
    const span = tracer.startSpan(operationName, {
      kind: SpanKind.CLIENT,
    }, parentContext);

    return span;
  }

  // Track API calls with OpenTelemetry
  traceApiCall(endpoint: string, method: string, startTime: number, endTime: number, status: number) {
    const span = this.startSpan(`HTTP ${method} ${endpoint}`);
    
    span.setAttributes({
      'http.method': method,
      'http.url': endpoint,
      'http.status_code': status,
      'service.name': this.serviceName,
      'environment': this.environment,
    });

    if (status >= 400) {
      span.setStatus({ code: SpanStatusCode.ERROR });
    } else {
      span.setStatus({ code: SpanStatusCode.OK });
    }

    // Simulate span timing
    span.end(endTime);

    // Convert to our trace format for Grafana
    const traceData: TraceData = {
      traceId: this.generateTraceId(),
      spanId: this.generateSpanId(),
      operationName: `HTTP ${method} ${endpoint}`,
      startTime,
      endTime,
      duration: endTime - startTime,
      status: status >= 400 ? 'error' : 'ok',
      tags: {
        'http.method': method,
        'http.url': endpoint,
        'http.status_code': status.toString(),
        'service.name': this.serviceName,
        'environment': this.environment,
      }
    };

    this.traces.push(traceData);
    this.flushTraces();
  }

  // Track user interactions as spans
  traceUserInteraction(action: string, component: string, metadata?: Record<string, any>) {
    const span = this.startSpan(`User ${action}`);
    
    span.setAttributes({
      'user.action': action,
      'component.name': component,
      'service.name': this.serviceName,
      'environment': this.environment,
      ...metadata,
    });

    span.setStatus({ code: SpanStatusCode.OK });
    span.end();

    const now = Date.now();
    const traceData: TraceData = {
      traceId: this.generateTraceId(),
      spanId: this.generateSpanId(),
      operationName: `User ${action}`,
      startTime: now - 100, // Simulate short duration
      endTime: now,
      duration: 100,
      status: 'ok',
      tags: {
        'user.action': action,
        'component.name': component,
        'service.name': this.serviceName,
        'environment': this.environment,
        ...metadata,
      }
    };

    this.traces.push(traceData);
  }

  // Track errors as spans
  traceError(error: Error | string, context?: Record<string, any>) {
    const span = this.startSpan('Error');
    
    const errorMessage = error instanceof Error ? error.message : error;
    const errorStack = error instanceof Error ? error.stack : undefined;

    span.setAttributes({
      'error.message': errorMessage,
      'error.stack': errorStack || '',
      'service.name': this.serviceName,
      'environment': this.environment,
      ...context,
    });

    span.setStatus({ 
      code: SpanStatusCode.ERROR,
      message: errorMessage 
    });
    span.end();

    const now = Date.now();
    const traceData: TraceData = {
      traceId: this.generateTraceId(),
      spanId: this.generateSpanId(),
      operationName: 'Error',
      startTime: now - 10,
      endTime: now,
      duration: 10,
      status: 'error',
      tags: {
        'error.message': errorMessage,
        'service.name': this.serviceName,
        'environment': this.environment,
        ...context,
      },
      logs: [{
        timestamp: now,
        fields: {
          level: 'error',
          message: errorMessage,
          stack: errorStack,
        }
      }]
    };

    this.traces.push(traceData);
    this.flushTraces();
  }

  // Track page loads and navigation
  tracePageLoad(url: string, loadTime: number) {
    const span = this.startSpan('Page Load');
    
    span.setAttributes({
      'page.url': url,
      'page.load_time': loadTime,
      'service.name': this.serviceName,
      'environment': this.environment,
    });

    span.setStatus({ code: SpanStatusCode.OK });
    span.end();

    const now = Date.now();
    const traceData: TraceData = {
      traceId: this.generateTraceId(),
      spanId: this.generateSpanId(),
      operationName: 'Page Load',
      startTime: now - loadTime,
      endTime: now,
      duration: loadTime,
      status: 'ok',
      tags: {
        'page.url': url,
        'page.load_time': loadTime.toString(),
        'service.name': this.serviceName,
        'environment': this.environment,
      }
    };

    this.traces.push(traceData);
  }

  // Convert traces to Jaeger format for Grafana
  private convertToJaegerFormat() {
    return {
      data: this.traces.map(trace => ({
        traceID: trace.traceId,
        spans: [{
          traceID: trace.traceId,
          spanID: trace.spanId,
          parentSpanID: trace.parentSpanId || '',
          operationName: trace.operationName,
          startTime: trace.startTime * 1000, // Convert to microseconds
          duration: trace.duration * 1000,
          tags: Object.entries(trace.tags).map(([key, value]) => ({
            key,
            value,
            type: 'string'
          })),
          logs: trace.logs || [],
          process: {
            serviceName: this.serviceName,
            tags: [
              { key: 'environment', value: this.environment, type: 'string' }
            ]
          }
        }]
      }))
    };
  }

  // Send traces to Grafana via our API endpoint
  private async flushTraces() {
    if (this.traces.length === 0) return;

    try {
      const jaegerData = this.convertToJaegerFormat();
      
      const endpoint = process.env.NEXT_PUBLIC_METRICS_ENDPOINT;
      if (!endpoint) return;

      await fetch(`${endpoint}/traces`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          traces: jaegerData,
          timestamp: Date.now(),
          environment: this.environment,
          serviceName: this.serviceName,
        })
      });

      // Clear sent traces
      this.traces = [];
    } catch (error) {
      console.error('Failed to send traces to Grafana:', error);
    }
  }

  // Generate trace and span IDs
  private generateTraceId(): string {
    return Math.random().toString(16).substr(2, 16);
  }

  private generateSpanId(): string {
    return Math.random().toString(16).substr(2, 8);
  }

  // Get current trace context
  getCurrentTraceContext() {
    const activeSpan = trace.getActiveSpan();
    if (activeSpan) {
      const spanContext = activeSpan.spanContext();
      return {
        traceId: spanContext.traceId,
        spanId: spanContext.spanId,
      };
    }
    return null;
  }

  // Manual flush for cleanup
  flush() {
    this.flushTraces();
  }
}

// Singleton instance
export const grafanaOTel = new GrafanaOTelCollector();

// Convenience functions
export const traceApiCall = (endpoint: string, method: string, startTime: number, endTime: number, status: number) => {
  grafanaOTel.traceApiCall(endpoint, method, startTime, endTime, status);
};

export const traceUserInteraction = (action: string, component: string, metadata?: Record<string, any>) => {
  grafanaOTel.traceUserInteraction(action, component, metadata);
};

export const traceError = (error: Error | string, context?: Record<string, any>) => {
  grafanaOTel.traceError(error, context);
};

export const tracePageLoad = (url: string, loadTime: number) => {
  grafanaOTel.tracePageLoad(url, loadTime);
};
