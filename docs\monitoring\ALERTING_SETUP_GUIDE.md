# Grafana Alerting Setup Guide for ARCA EMR

This guide will help you set up comprehensive alerting for your ARCA EMR QA environment.

## Overview

The alerting system monitors critical aspects of your EMR application:
- **Application Health**: Error rates, outages, performance
- **User Experience**: Core Web Vitals, response times
- **Business Metrics**: Authentication, API availability
- **Security**: Authentication failures, unusual patterns

## Step 1: Configure Contact Points

Contact points define where alerts are sent.

### Email Notifications

1. **Go to Alerting** → **Contact Points**
2. **Click "New contact point"**
3. **Configure Email**:
   ```
   Name: ARCA EMR Email Alerts
   Type: Email
   Addresses: <EMAIL>, <EMAIL>
   Subject: ARCA EMR Alert: {{ .GroupLabels.alertname }}
   Message: 
   Alert: {{ .GroupLabels.alertname }}
   Severity: {{ .GroupLabels.severity }}
   Description: {{ .CommonAnnotations.description }}
   Time: {{ .CommonAnnotations.startsAt }}
   Runbook: {{ .CommonAnnotations.runbook_url }}
   ```

### Slack Notifications (Optional)

1. **Create Slack Webhook**:
   - Go to your Slack workspace
   - Create a new app or use existing
   - Add "Incoming Webhooks" feature
   - Create webhook for your alerts channel
   - Copy the webhook URL

2. **Configure in Grafana**:
   ```
   Name: ARCA EMR Slack Alerts
   Type: Slack
   Webhook URL: [Your Slack Webhook URL]
   Channel: #arca-emr-alerts
   Username: Grafana
   Title: ARCA EMR Alert: {{ .GroupLabels.alertname }}
   Text: 
   {{ .CommonAnnotations.summary }}
   *Severity:* {{ .GroupLabels.severity }}
   *Description:* {{ .CommonAnnotations.description }}
   *Runbook:* {{ .CommonAnnotations.runbook_url }}
   ```

## Step 2: Create Alert Rules

### Critical Alerts

#### 1. High Frontend Error Rate
```
Name: ARCA EMR - High Frontend Error Rate
Query: sum(rate(arca_emr_frontend_error_count[5m])) * 60
Condition: IS ABOVE 5
Evaluation: Every 1m for 2m
Labels:
  severity: critical
  team: frontend
  service: arca-emr
Annotations:
  summary: High error rate detected in ARCA EMR frontend
  description: Frontend error rate is above 5 errors per minute
```

#### 2. Application Outage Detection
```
Name: ARCA EMR - Low User Activity (Possible Outage)
Query: sum(rate(arca_emr_page_view[5m]))
Condition: IS BELOW 0.01
Evaluation: Every 1m for 5m
Labels:
  severity: critical
  team: sre
  service: arca-emr
Annotations:
  summary: Possible application outage detected
  description: Page view rate is extremely low
```

#### 3. High API Error Rate
```
Name: ARCA EMR - High API Error Rate
Query: sum(rate(arca_emr_api_call_count{success="false"}[5m])) / sum(rate(arca_emr_api_call_count[5m])) * 100
Condition: IS ABOVE 10
Evaluation: Every 1m for 2m
Labels:
  severity: critical
  team: backend
  service: arca-emr
Annotations:
  summary: High API error rate detected
  description: API error rate is above 10%
```

### Warning Alerts

#### 4. Slow API Response Times
```
Name: ARCA EMR - Slow API Response Times
Query: histogram_quantile(0.95, rate(arca_emr_api_call_duration_bucket[5m]))
Condition: IS ABOVE 5000
Evaluation: Every 1m for 3m
Labels:
  severity: warning
  team: backend
  service: arca-emr
Annotations:
  summary: API response times are degraded
  description: 95th percentile API response time is above 5 seconds
```

#### 5. Poor Core Web Vitals
```
Name: ARCA EMR - Poor Core Web Vitals
Query: avg(arca_emr_performance_largest_contentful_paint)
Condition: IS ABOVE 4000
Evaluation: Every 5m for 10m
Labels:
  severity: warning
  team: frontend
  service: arca-emr
Annotations:
  summary: Core Web Vitals performance is degraded
  description: Largest Contentful Paint (LCP) is above 4 seconds
```

#### 6. Authentication Issues
```
Name: ARCA EMR - High Authentication Failure Rate
Query: sum(rate(arca_emr_emr_error{error_type="authentication"}[5m])) * 60
Condition: IS ABOVE 3
Evaluation: Every 1m for 2m
Labels:
  severity: warning
  team: security
  service: arca-emr
Annotations:
  summary: High authentication failure rate
  description: Authentication failure rate is above 3 per minute
```

## Step 3: Configure Notification Policies

Notification policies determine which alerts go to which contact points.

### Basic Policy Structure

1. **Go to Alerting** → **Notification policies**
2. **Edit the default policy** or create new ones:

```
Default Policy:
- Receiver: ARCA EMR Email Alerts
- Group by: alertname, severity
- Group wait: 30s
- Group interval: 5m
- Repeat interval: 1h
- Matchers: service = arca-emr

Critical Alert Policy (nested under default):
- Receiver: ARCA EMR Slack Alerts
- Group wait: 10s
- Group interval: 2m
- Repeat interval: 30m
- Matchers: severity = critical
```

## Step 4: Test Your Alerts

### Manual Testing

1. **Create Test Alert**:
   ```
   Name: Test Alert
   Query: vector(1)
   Condition: IS ABOVE 0
   Evaluation: Every 1m for 0m
   ```

2. **Verify Notifications**:
   - Check email inbox
   - Check Slack channel (if configured)
   - Verify alert appears in Grafana

3. **Delete Test Alert** after verification

### Automated Testing

Create a simple script to generate test metrics:

```javascript
// Test error generation
console.error("Test error for monitoring");

// Test API call simulation
fetch('/api/test-endpoint-that-does-not-exist')
  .catch(error => console.log('Expected test error'));
```

## Step 5: Alert Runbooks

Create runbooks for each alert type:

### High Error Rate Runbook

1. **Check Recent Deployments**:
   - Was there a recent deployment?
   - Check deployment logs in Vercel

2. **Investigate Error Details**:
   - Go to Grafana Logs
   - Filter by error level
   - Identify common error patterns

3. **Check External Dependencies**:
   - Azure AD B2C status
   - API Gateway status
   - Database connectivity

4. **Escalation Path**:
   - If errors persist > 10 minutes: Page on-call engineer
   - If errors affect > 50% of users: Escalate to management

### Performance Degradation Runbook

1. **Check System Resources**:
   - API response times by endpoint
   - Database query performance
   - CDN performance

2. **Identify Bottlenecks**:
   - Slow API endpoints
   - Large payload responses
   - Database query timeouts

3. **Immediate Actions**:
   - Scale backend services if possible
   - Enable caching where appropriate
   - Consider feature flags to disable heavy features

### Outage Response Runbook

1. **Verify Outage**:
   - Check multiple monitoring sources
   - Verify from different locations
   - Confirm with user reports

2. **Immediate Response**:
   - Check Vercel deployment status
   - Verify DNS resolution
   - Check CDN status

3. **Communication**:
   - Update status page
   - Notify stakeholders
   - Prepare user communication

## Step 6: Alert Maintenance

### Regular Reviews

1. **Weekly Alert Review**:
   - Check for false positives
   - Adjust thresholds based on normal patterns
   - Review alert fatigue metrics

2. **Monthly Optimization**:
   - Analyze alert effectiveness
   - Update runbooks based on incidents
   - Add new alerts for discovered issues

### Alert Tuning

1. **Threshold Adjustment**:
   - Monitor baseline metrics for 1-2 weeks
   - Set thresholds at 2-3 standard deviations from normal
   - Adjust based on business impact

2. **Timing Optimization**:
   - Critical alerts: 1-2 minutes
   - Warning alerts: 3-5 minutes
   - Informational: 10+ minutes

## Best Practices

### Alert Design

1. **Actionable Alerts**: Every alert should have a clear action
2. **Meaningful Names**: Use descriptive alert names
3. **Proper Severity**: Critical = immediate action, Warning = investigate soon
4. **Good Descriptions**: Include context and next steps

### Notification Management

1. **Avoid Alert Fatigue**: Don't over-alert
2. **Escalation Paths**: Define clear escalation procedures
3. **Quiet Hours**: Consider reducing non-critical alerts during off-hours
4. **Alert Grouping**: Group related alerts to reduce noise

### Documentation

1. **Keep Runbooks Updated**: Regular review and updates
2. **Document Changes**: Track alert modifications
3. **Share Knowledge**: Ensure team knows alert procedures
4. **Post-Incident Reviews**: Update alerts based on incidents

## Troubleshooting

### Alerts Not Firing

1. **Check Query Syntax**: Test queries in Explore
2. **Verify Data**: Ensure metrics are being collected
3. **Check Evaluation**: Verify evaluation frequency and duration
4. **Review Conditions**: Confirm threshold values are correct

### Missing Notifications

1. **Check Contact Points**: Test contact point configuration
2. **Review Notification Policies**: Verify routing rules
3. **Check Spam Filters**: Ensure emails aren't blocked
4. **Verify Webhooks**: Test Slack/webhook connectivity

### False Positives

1. **Adjust Thresholds**: Based on normal operational patterns
2. **Increase Evaluation Time**: Reduce noise from temporary spikes
3. **Add Context**: Use additional labels for better filtering
4. **Review Queries**: Ensure queries capture the right metrics
