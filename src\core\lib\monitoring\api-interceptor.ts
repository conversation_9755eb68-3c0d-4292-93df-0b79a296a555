/**
 * API Interceptor for automatic metrics collection
 * Integrates with existing axios interceptor to track API performance
 */

import { AxiosResponse, AxiosError, InternalAxiosRequestConfig } from 'axios';
import { recordApiCall, recordError } from './metrics';
import { traceApiCall, traceError } from './otel-grafana';

interface RequestTiming {
  startTime: number;
  endpoint: string;
  method: string;
}

// Store request timings
const requestTimings = new Map<string, RequestTiming>();

// Generate unique request ID
const generateRequestId = (): string => {
  return `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
};

// Request interceptor to track start time
export const metricsRequestInterceptor = (
  config: InternalAxiosRequestConfig
) => {
  const requestId = generateRequestId();
  const startTime = performance.now();

  // Store timing info
  requestTimings.set(requestId, {
    startTime,
    endpoint: config.url || 'unknown',
    method: (config.method || 'GET').toUpperCase(),
  });

  // Add request ID to headers for tracking
  config.headers = config.headers || {};
  config.headers['X-Request-ID'] = requestId;

  return config;
};

// Response interceptor to calculate duration and record metrics
export const metricsResponseInterceptor = (response: AxiosResponse) => {
  const requestId = response.config.headers?.['X-Request-ID'] as string;
  const timing = requestTimings.get(requestId);

  if (timing) {
    const duration = performance.now() - timing.startTime;
    const endpoint = cleanEndpoint(timing.endpoint);

    // Record API call metrics
    recordApiCall(endpoint, timing.method, duration, response.status);

    // Record OpenTelemetry trace
    const endTime = timing.startTime + duration;
    traceApiCall(
      endpoint,
      timing.method,
      timing.startTime,
      endTime,
      response.status
    );

    // Clean up timing data
    requestTimings.delete(requestId);
  }

  return response;
};

// Error interceptor to track failed requests
export const metricsErrorInterceptor = (error: AxiosError) => {
  const requestId = error.config?.headers?.['X-Request-ID'] as string;
  const timing = requestTimings.get(requestId);

  if (timing) {
    const duration = performance.now() - timing.startTime;
    const endpoint = cleanEndpoint(timing.endpoint);
    const status = error.response?.status || 0;

    // Record API call metrics for failed requests
    recordApiCall(endpoint, timing.method, duration, status);

    // Record error details
    recordError(error, {
      endpoint,
      method: timing.method,
      status: status.toString(),
      response_data: error.response?.data,
    });

    // Record OpenTelemetry trace for error
    const endTime = timing.startTime + duration;
    traceApiCall(endpoint, timing.method, timing.startTime, endTime, status);
    traceError(error, {
      endpoint,
      method: timing.method,
      status: status.toString(),
    });

    // Clean up timing data
    requestTimings.delete(requestId);
  }

  return Promise.reject(error);
};

// Clean endpoint URL for better grouping in metrics
const cleanEndpoint = (url: string): string => {
  if (!url) return 'unknown';

  // Remove base URL
  const baseUrl = process.env.NEXT_PUBLIC_API_URL || '';
  let cleanUrl = url.replace(baseUrl, '');

  // Remove query parameters
  cleanUrl = cleanUrl.split('?')[0];

  // Replace dynamic segments with placeholders
  cleanUrl = cleanUrl
    .replace(/\/\d+/g, '/:id') // Replace numeric IDs
    .replace(/\/[a-f0-9-]{36}/g, '/:uuid') // Replace UUIDs
    .replace(/\/[a-f0-9-]{8,}/g, '/:hash'); // Replace long hashes

  return cleanUrl || '/';
};

// Cleanup function for memory management
export const cleanupRequestTimings = () => {
  const now = performance.now();
  const timeout = 5 * 60 * 1000; // 5 minutes

  requestTimings.forEach((timing, requestId) => {
    if (now - timing.startTime > timeout) {
      requestTimings.delete(requestId);
    }
  });
};

// Run cleanup every 5 minutes
if (typeof window !== 'undefined') {
  setInterval(cleanupRequestTimings, 5 * 60 * 1000);
}
