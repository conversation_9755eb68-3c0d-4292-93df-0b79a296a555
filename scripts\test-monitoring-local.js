/**
 * Local Monitoring Test Script
 * Run this in browser console to test monitoring functionality
 */

// Test 1: Basic Metrics Collection
console.log('🧪 Testing ARCA EMR Monitoring System Locally...');

// Test 2: Record Custom Metric
if (window.metricsCollector) {
  console.log('✅ Metrics collector found');

  // Test custom metric
  window.metricsCollector.recordMetric('test_metric', 1, {
    test_type: 'local_testing',
    timestamp: Date.now(),
  });
  console.log('📊 Custom metric recorded');
} else {
  console.log('❌ Metrics collector not found');
}

// Test 3: Test User Interaction Tracking
if (window.recordUserInteraction) {
  window.recordUserInteraction('test_click', 'test_component', {
    test_mode: 'local',
  });
  console.log('👆 User interaction recorded');
}

// Test 4: Test Error Tracking
if (window.recordError) {
  window.recordError(new Error('Test error for monitoring'), {
    test_context: 'local_testing',
    component: 'test_script',
  });
  console.log('🚨 Test error recorded');
}

// Test 5: Test API Call Simulation
fetch('/api/metrics')
  .then((response) => {
    console.log('🌐 API health check:', response.status);
    return response.json();
  })
  .then((data) => {
    console.log('📡 API response:', data);
  })
  .catch((error) => {
    console.log('❌ API test failed:', error);
  });

// Test 6: Generate Test Performance Metric
if (window.performance && window.performance.mark) {
  performance.mark('test-start');
  setTimeout(() => {
    performance.mark('test-end');
    performance.measure('test-operation', 'test-start', 'test-end');
    console.log('⏱️ Performance metric generated');
  }, 100);
}

// Test 7: Check if metrics are being sent
setTimeout(() => {
  console.log('🔍 Check Network tab for POST requests to /api/metrics');
  console.log('📊 Metrics should be sent every 30 seconds');
  console.log('✅ Local monitoring test completed!');
}, 2000);

// Export test functions for manual testing
window.testMonitoring = {
  recordTestMetric: () => {
    if (window.metricsCollector) {
      window.metricsCollector.recordMetric('manual_test', Math.random(), {
        timestamp: Date.now(),
        test_type: 'manual',
      });
      console.log('📊 Manual test metric recorded');
    }
  },

  recordTestError: () => {
    if (window.recordError) {
      window.recordError(new Error('Manual test error'), {
        test_type: 'manual',
        timestamp: Date.now(),
      });
      console.log('🚨 Manual test error recorded');
    }
  },

  recordTestInteraction: () => {
    if (window.recordUserInteraction) {
      window.recordUserInteraction('manual_test_click', 'test_button', {
        test_type: 'manual',
        timestamp: Date.now(),
      });
      console.log('👆 Manual test interaction recorded');
    }
  },

  checkMetricsBuffer: () => {
    if (window.metricsCollector && window.metricsCollector.getBufferStatus) {
      const status = window.metricsCollector.getBufferStatus();
      console.log('📊 Metrics buffer status:', status);
    } else {
      console.log('❌ Cannot access metrics buffer');
    }
  },
};

console.log('🎮 Manual test functions available as window.testMonitoring');
console.log('Example: window.testMonitoring.recordTestMetric()');

// Enhanced testing functions for dashboard data
window.testMonitoring.generateDashboardData = () => {
  console.log('🎯 Generating realistic data for dashboard testing...');

  // Simulate patient management activities
  const patientActions = ['view', 'create', 'update', 'search'];
  patientActions.forEach((action, index) => {
    setTimeout(() => {
      window.recordUserInteraction(`patient_${action}`, 'patient_management', {
        patient_id: `test_patient_${index}`,
        test_mode: 'dashboard_data',
      });

      // Simulate API calls
      window.recordApiCall(
        `/api/patients/${action}`,
        'GET',
        Math.random() * 1000 + 200,
        200
      );
    }, index * 1000);
  });

  // Simulate consultation workflow
  setTimeout(() => {
    window.recordUserInteraction('consultation_start', 'consultation_module', {
      consultation_id: 'test_consultation_1',
      test_mode: 'dashboard_data',
    });
    window.recordApiCall(
      '/api/consultations',
      'POST',
      Math.random() * 2000 + 500,
      201
    );
  }, 5000);

  // Simulate prescription activities
  setTimeout(() => {
    window.recordUserInteraction('prescription_create', 'prescription_module', {
      prescription_id: 'test_prescription_1',
      test_mode: 'dashboard_data',
    });
    window.recordApiCall(
      '/api/prescriptions',
      'POST',
      Math.random() * 1500 + 300,
      201
    );
  }, 6000);

  // Simulate some errors for error tracking
  setTimeout(() => {
    window.recordError(new Error('Test API timeout'), {
      endpoint: '/api/lab-tests',
      method: 'GET',
      test_mode: 'dashboard_data',
    });
    window.recordApiCall('/api/lab-tests', 'GET', 5000, 500);
  }, 7000);

  // Simulate performance metrics
  setTimeout(() => {
    // Simulate Core Web Vitals
    if (window.metricsCollector) {
      window.metricsCollector.recordPerformance(
        'largest_contentful_paint',
        Math.random() * 3000 + 1000
      );
      window.metricsCollector.recordPerformance(
        'first_input_delay',
        Math.random() * 100 + 50
      );
      window.metricsCollector.recordPerformance(
        'cumulative_layout_shift',
        Math.random() * 0.1
      );
    }
  }, 8000);

  console.log('✅ Dashboard test data generation completed!');
  console.log('🔍 Check Grafana dashboards in 2-3 minutes for data');
};

window.testMonitoring.simulateUserSession = () => {
  console.log('👤 Simulating complete user session...');

  // Login simulation
  window.recordUserInteraction('login_attempt', 'auth_module', {
    test_mode: 'user_session',
  });
  window.recordApiCall('/api/auth/login', 'POST', 800, 200);

  setTimeout(() => {
    // Dashboard view
    window.recordUserInteraction('dashboard_view', 'dashboard', {
      test_mode: 'user_session',
    });
    window.recordApiCall('/api/dashboard/stats', 'GET', 400, 200);
  }, 2000);

  setTimeout(() => {
    // Patient search and view
    window.recordUserInteraction('patient_search', 'search_component', {
      query: 'test patient',
      test_mode: 'user_session',
    });
    window.recordApiCall('/api/patients/search', 'GET', 600, 200);
  }, 4000);

  setTimeout(() => {
    // Form submission
    window.recordUserInteraction('form_submit', 'patient_form', {
      form_type: 'demographics',
      test_mode: 'user_session',
    });
    window.recordApiCall('/api/patients/123/demographics', 'PUT', 1200, 200);
  }, 6000);

  setTimeout(() => {
    // Logout
    window.recordUserInteraction('logout', 'auth_module', {
      test_mode: 'user_session',
    });
    window.recordApiCall('/api/auth/logout', 'POST', 300, 200);
  }, 8000);

  console.log('✅ User session simulation completed!');
};

console.log('🚀 Enhanced testing functions:');
console.log(
  '- window.testMonitoring.generateDashboardData() - Generate realistic dashboard data'
);
console.log(
  '- window.testMonitoring.simulateUserSession() - Simulate complete user journey'
);
