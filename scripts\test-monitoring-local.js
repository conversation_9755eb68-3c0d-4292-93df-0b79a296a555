/**
 * Local Monitoring Test Script
 * Run this in browser console to test monitoring functionality
 */

// Test 1: Basic Metrics Collection
console.log('🧪 Testing ARCA EMR Monitoring System Locally...');

// Test 2: Record Custom Metric
if (window.metricsCollector) {
  console.log('✅ Metrics collector found');
  
  // Test custom metric
  window.metricsCollector.recordMetric('test_metric', 1, {
    test_type: 'local_testing',
    timestamp: Date.now()
  });
  console.log('📊 Custom metric recorded');
} else {
  console.log('❌ Metrics collector not found');
}

// Test 3: Test User Interaction Tracking
if (window.recordUserInteraction) {
  window.recordUserInteraction('test_click', 'test_component', {
    test_mode: 'local'
  });
  console.log('👆 User interaction recorded');
}

// Test 4: Test Error Tracking
if (window.recordError) {
  window.recordError(new Error('Test error for monitoring'), {
    test_context: 'local_testing',
    component: 'test_script'
  });
  console.log('🚨 Test error recorded');
}

// Test 5: Test API Call Simulation
fetch('/api/metrics')
  .then(response => {
    console.log('🌐 API health check:', response.status);
    return response.json();
  })
  .then(data => {
    console.log('📡 API response:', data);
  })
  .catch(error => {
    console.log('❌ API test failed:', error);
  });

// Test 6: Generate Test Performance Metric
if (window.performance && window.performance.mark) {
  performance.mark('test-start');
  setTimeout(() => {
    performance.mark('test-end');
    performance.measure('test-operation', 'test-start', 'test-end');
    console.log('⏱️ Performance metric generated');
  }, 100);
}

// Test 7: Check if metrics are being sent
setTimeout(() => {
  console.log('🔍 Check Network tab for POST requests to /api/metrics');
  console.log('📊 Metrics should be sent every 30 seconds');
  console.log('✅ Local monitoring test completed!');
}, 2000);

// Export test functions for manual testing
window.testMonitoring = {
  recordTestMetric: () => {
    if (window.metricsCollector) {
      window.metricsCollector.recordMetric('manual_test', Math.random(), {
        timestamp: Date.now(),
        test_type: 'manual'
      });
      console.log('📊 Manual test metric recorded');
    }
  },
  
  recordTestError: () => {
    if (window.recordError) {
      window.recordError(new Error('Manual test error'), {
        test_type: 'manual',
        timestamp: Date.now()
      });
      console.log('🚨 Manual test error recorded');
    }
  },
  
  recordTestInteraction: () => {
    if (window.recordUserInteraction) {
      window.recordUserInteraction('manual_test_click', 'test_button', {
        test_type: 'manual',
        timestamp: Date.now()
      });
      console.log('👆 Manual test interaction recorded');
    }
  },
  
  checkMetricsBuffer: () => {
    if (window.metricsCollector && window.metricsCollector.getBufferStatus) {
      const status = window.metricsCollector.getBufferStatus();
      console.log('📊 Metrics buffer status:', status);
    } else {
      console.log('❌ Cannot access metrics buffer');
    }
  }
};

console.log('🎮 Manual test functions available as window.testMonitoring');
console.log('Example: window.testMonitoring.recordTestMetric()');
