/**
 * EMR-Specific Metrics Collection
 * Custom metrics for medical record management, patient interactions, and clinical workflows
 */

import { recordMetric, recordUserInteraction } from './metrics';

// Patient Management Metrics
export const trackPatientView = (patientId: string, viewType: 'summary' | 'details' | 'history') => {
  recordMetric('patient_view', 1, {
    patient_id: patientId,
    view_type: viewType,
    action: 'view'
  });
  
  recordUserInteraction('patient_view', 'PatientComponent', {
    patientId,
    viewType
  });
};

export const trackPatientSearch = (query: string, resultsCount: number, searchType: 'name' | 'id' | 'phone') => {
  recordMetric('patient_search', 1, {
    search_type: searchType,
    has_results: resultsCount > 0 ? 'true' : 'false',
    results_count: resultsCount.toString()
  });
  
  recordUserInteraction('search', 'PatientSearch', {
    query: query.length > 0 ? 'has_query' : 'empty_query',
    searchType,
    resultsCount
  });
};

export const trackPatientRegistration = (success: boolean, registrationType: 'new' | 'existing') => {
  recordMetric('patient_registration', 1, {
    success: success.toString(),
    registration_type: registrationType
  });
  
  recordUserInteraction('patient_registration', 'PatientRegistration', {
    success,
    registrationType
  });
};

// Consultation Metrics
export const trackConsultationStart = (consultationId: string, consultationType: 'new' | 'followup') => {
  recordMetric('consultation_start', 1, {
    consultation_id: consultationId,
    consultation_type: consultationType
  });
  
  recordUserInteraction('consultation_start', 'ConsultationComponent', {
    consultationId,
    consultationType
  });
};

export const trackConsultationComplete = (consultationId: string, duration: number, hasNotes: boolean) => {
  recordMetric('consultation_complete', 1, {
    consultation_id: consultationId,
    has_notes: hasNotes.toString()
  });
  
  recordMetric('consultation_duration', duration, {
    consultation_id: consultationId
  });
  
  recordUserInteraction('consultation_complete', 'ConsultationComponent', {
    consultationId,
    duration,
    hasNotes
  });
};

// Prescription Metrics
export const trackPrescriptionCreate = (prescriptionId: string, medicationCount: number) => {
  recordMetric('prescription_create', 1, {
    prescription_id: prescriptionId,
    medication_count: medicationCount.toString()
  });
  
  recordUserInteraction('prescription_create', 'PrescriptionComponent', {
    prescriptionId,
    medicationCount
  });
};

export const trackPrescriptionPrint = (prescriptionId: string, printType: 'pdf' | 'direct') => {
  recordMetric('prescription_print', 1, {
    prescription_id: prescriptionId,
    print_type: printType
  });
  
  recordUserInteraction('prescription_print', 'PrescriptionComponent', {
    prescriptionId,
    printType
  });
};

// Lab Test Metrics
export const trackLabTestOrder = (testIds: string[], patientId: string) => {
  recordMetric('lab_test_order', testIds.length, {
    patient_id: patientId,
    test_count: testIds.length.toString()
  });
  
  recordUserInteraction('lab_test_order', 'LabTestComponent', {
    patientId,
    testCount: testIds.length,
    testIds: testIds.join(',')
  });
};

export const trackLabResultView = (testId: string, resultType: 'normal' | 'abnormal' | 'pending') => {
  recordMetric('lab_result_view', 1, {
    test_id: testId,
    result_type: resultType
  });
  
  recordUserInteraction('lab_result_view', 'LabResultComponent', {
    testId,
    resultType
  });
};

// Lifestyle Monitoring Metrics
export const trackLifestyleFormSubmit = (formType: string, patientId: string, success: boolean) => {
  recordMetric('lifestyle_form_submit', 1, {
    form_type: formType,
    patient_id: patientId,
    success: success.toString()
  });
  
  recordUserInteraction('lifestyle_form_submit', 'LifestyleComponent', {
    formType,
    patientId,
    success
  });
};

export const trackNutritionDataEntry = (patientId: string, dataType: 'manual' | 'imported', itemCount: number) => {
  recordMetric('nutrition_data_entry', itemCount, {
    patient_id: patientId,
    data_type: dataType,
    item_count: itemCount.toString()
  });
  
  recordUserInteraction('nutrition_data_entry', 'NutritionComponent', {
    patientId,
    dataType,
    itemCount
  });
};

// Ambient Listening Metrics
export const trackAmbientListeningStart = (sessionId: string, microphoneType: 'single' | 'dual') => {
  recordMetric('ambient_listening_start', 1, {
    session_id: sessionId,
    microphone_type: microphoneType
  });
  
  recordUserInteraction('ambient_listening_start', 'AmbientListeningComponent', {
    sessionId,
    microphoneType
  });
};

export const trackAmbientListeningStop = (sessionId: string, duration: number, transcriptionLength: number) => {
  recordMetric('ambient_listening_stop', 1, {
    session_id: sessionId,
    duration: duration.toString(),
    transcription_length: transcriptionLength.toString()
  });
  
  recordMetric('ambient_listening_duration', duration, {
    session_id: sessionId
  });
  
  recordUserInteraction('ambient_listening_stop', 'AmbientListeningComponent', {
    sessionId,
    duration,
    transcriptionLength
  });
};

// Authentication Metrics
export const trackUserLogin = (userId: string, loginMethod: 'popup' | 'redirect') => {
  recordMetric('user_login', 1, {
    user_id: userId,
    login_method: loginMethod
  });
  
  recordUserInteraction('user_login', 'AuthComponent', {
    userId,
    loginMethod
  });
};

export const trackUserLogout = (userId: string, sessionDuration: number) => {
  recordMetric('user_logout', 1, {
    user_id: userId,
    session_duration: sessionDuration.toString()
  });
  
  recordMetric('session_duration', sessionDuration, {
    user_id: userId
  });
  
  recordUserInteraction('user_logout', 'AuthComponent', {
    userId,
    sessionDuration
  });
};

// Dashboard Metrics
export const trackDashboardView = (dashboardType: 'emr' | 'mrd' | 'lifestyle', widgetCount: number) => {
  recordMetric('dashboard_view', 1, {
    dashboard_type: dashboardType,
    widget_count: widgetCount.toString()
  });
  
  recordUserInteraction('dashboard_view', 'DashboardComponent', {
    dashboardType,
    widgetCount
  });
};

export const trackWidgetInteraction = (widgetName: string, action: 'expand' | 'collapse' | 'refresh' | 'filter') => {
  recordMetric('widget_interaction', 1, {
    widget_name: widgetName,
    action
  });
  
  recordUserInteraction('widget_interaction', 'WidgetComponent', {
    widgetName,
    action
  });
};

// Print Functionality Metrics
export const trackPrintAction = (documentType: string, patientId?: string, success: boolean = true) => {
  recordMetric('print_action', 1, {
    document_type: documentType,
    ...(patientId && { patient_id: patientId }),
    success: success.toString()
  });
  
  recordUserInteraction('print_action', 'PrintComponent', {
    documentType,
    patientId,
    success
  });
};

// Error Tracking for EMR-specific errors
export const trackEMRError = (errorType: string, component: string, context?: Record<string, any>) => {
  recordMetric('emr_error', 1, {
    error_type: errorType,
    component,
    ...context
  });
  
  recordUserInteraction('emr_error', component, {
    errorType,
    ...context
  });
};

// Performance Metrics for EMR-specific operations
export const trackEMRPerformance = (operation: string, duration: number, context?: Record<string, any>) => {
  recordMetric('emr_performance', duration, {
    operation,
    ...context
  });
};
