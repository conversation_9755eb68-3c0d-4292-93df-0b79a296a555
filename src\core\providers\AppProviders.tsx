'use client';

import React, { <PERSON> } from 'react';

import { Toaster } from 'sonner';

import dynamic from 'next/dynamic';

import StatusModal from '@core/components/status-modal';

import AuthProvider from './AuthProviders';
import MuiThemeProvider from './MuiThemeProvider';
import MonitoringProvider from './MonitoringProvider';

const StageWise = dynamic(() => import('@/core/components/stage-wise'), {
  ssr: false,
});

type Props = {
  children: React.ReactNode;
};

const AppProviders: FC<Props> = ({ children }) => {
  return (
    <MonitoringProvider>
      <AuthProvider>
        <MuiThemeProvider>
          <Toaster richColors closeButton />
          <StageWise />
          <StatusModal />
          {children}
        </MuiThemeProvider>
      </AuthProvider>
    </MonitoringProvider>
  );
};

export default AppProviders;
