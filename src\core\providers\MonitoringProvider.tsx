/**
 * Monitoring Provider
 * Initializes monitoring across the application and provides monitoring context
 */

'use client';

import React, { createContext, useContext, useEffect, ReactNode } from 'react';
import { usePathname } from 'next/navigation';
import { metricsCollector, setUserId } from '@/core/lib/monitoring/metrics';
import { trackUserLogin, trackDashboardView } from '@/core/lib/monitoring/emr-metrics';

interface MonitoringContextType {
  isMonitoringEnabled: boolean;
  environment: string;
}

const MonitoringContext = createContext<MonitoringContextType>({
  isMonitoringEnabled: false,
  environment: 'development'
});

interface MonitoringProviderProps {
  children: ReactNode;
}

export const MonitoringProvider: React.FC<MonitoringProviderProps> = ({ children }) => {
  const pathname = usePathname();
  const environment = process.env.NEXT_PUBLIC_NODE_ENV || 'development';
  const isMonitoringEnabled = environment !== 'development' || process.env.NEXT_PUBLIC_ENABLE_DEV_MONITORING === 'true';

  useEffect(() => {
    if (!isMonitoringEnabled) return;

    // Initialize monitoring
    console.log('Monitoring initialized for environment:', environment);

    // Track page navigation
    const trackPageView = () => {
      metricsCollector.recordMetric('page_navigation', 1, {
        from_path: document.referrer ? new URL(document.referrer).pathname : 'direct',
        to_path: pathname,
        navigation_type: 'client_side'
      });
    };

    trackPageView();

    // Track specific dashboard views
    if (pathname.includes('/emr')) {
      trackDashboardView('emr', 0); // Widget count will be updated by individual components
    } else if (pathname.includes('/mrd')) {
      trackDashboardView('mrd', 0);
    } else if (pathname.includes('/lifestyle')) {
      trackDashboardView('lifestyle', 0);
    }

    // Cleanup function
    return () => {
      // Flush any remaining metrics when component unmounts
      metricsCollector.flush();
    };
  }, [pathname, isMonitoringEnabled, environment]);

  // Global error boundary for monitoring
  useEffect(() => {
    if (!isMonitoringEnabled) return;

    const handleGlobalError = (event: ErrorEvent) => {
      metricsCollector.recordError(event.error || event.message, {
        filename: event.filename,
        lineno: event.lineno,
        colno: event.colno,
        type: 'global_error'
      });
    };

    const handleUnhandledRejection = (event: PromiseRejectionEvent) => {
      metricsCollector.recordError(event.reason, {
        type: 'unhandled_promise_rejection'
      });
    };

    window.addEventListener('error', handleGlobalError);
    window.addEventListener('unhandledrejection', handleUnhandledRejection);

    return () => {
      window.removeEventListener('error', handleGlobalError);
      window.removeEventListener('unhandledrejection', handleUnhandledRejection);
    };
  }, [isMonitoringEnabled]);

  // Track user session
  useEffect(() => {
    if (!isMonitoringEnabled) return;

    // Track session start
    metricsCollector.recordMetric('session_start', 1, {
      entry_point: pathname,
      user_agent: navigator.userAgent,
      screen_resolution: `${screen.width}x${screen.height}`,
      viewport_size: `${window.innerWidth}x${window.innerHeight}`
    });

    // Track session end on page unload
    const handleBeforeUnload = () => {
      metricsCollector.recordMetric('session_end', 1, {
        exit_point: pathname,
        session_duration: (Date.now() - performance.timeOrigin).toString()
      });
      metricsCollector.flush(); // Ensure metrics are sent before page unloads
    };

    window.addEventListener('beforeunload', handleBeforeUnload);

    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload);
    };
  }, [isMonitoringEnabled, pathname]);

  const contextValue: MonitoringContextType = {
    isMonitoringEnabled,
    environment
  };

  return (
    <MonitoringContext.Provider value={contextValue}>
      {children}
    </MonitoringContext.Provider>
  );
};

// Hook to use monitoring context
export const useMonitoringContext = () => {
  const context = useContext(MonitoringContext);
  if (!context) {
    throw new Error('useMonitoringContext must be used within a MonitoringProvider');
  }
  return context;
};

// Higher-order component for automatic component monitoring
export const withMonitoring = <P extends object>(
  WrappedComponent: React.ComponentType<P>,
  componentName: string
) => {
  const MonitoredComponent: React.FC<P> = (props) => {
    const { isMonitoringEnabled } = useMonitoringContext();

    useEffect(() => {
      if (!isMonitoringEnabled) return;

      const startTime = Date.now();

      // Track component mount
      metricsCollector.recordMetric('component_mount', 1, {
        component_name: componentName,
        mount_time: startTime.toString()
      });

      return () => {
        // Track component unmount and time spent
        const endTime = Date.now();
        const timeSpent = endTime - startTime;

        metricsCollector.recordMetric('component_unmount', 1, {
          component_name: componentName,
          time_spent: timeSpent.toString()
        });

        metricsCollector.recordMetric('component_time_spent', timeSpent, {
          component_name: componentName
        });
      };
    }, [isMonitoringEnabled]);

    return <WrappedComponent {...props} />;
  };

  MonitoredComponent.displayName = `withMonitoring(${componentName})`;
  return MonitoredComponent;
};

export default MonitoringProvider;
