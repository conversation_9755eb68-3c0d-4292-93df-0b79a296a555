import { AzureMonitorTraceExporter } from '@azure/monitor-opentelemetry-exporter';
import { registerOTel } from '@vercel/otel';

export async function register() {
  const environment = process.env.NEXT_PUBLIC_NODE_ENV || 'development';
  const isProduction = environment === 'production';
  const isQA =
    process.env.VERCEL_ENV === 'production' &&
    process.env.VERCEL_URL?.includes('vercel.app');

  // Configure service name based on environment
  const serviceName = isQA
    ? 'arca-emr-qa'
    : isProduction
      ? 'arca-emr-prod'
      : 'arca-emr-dev';

  if (
    isProduction &&
    !isQA &&
    process.env.APPLICATIONINSIGHTS_CONNECTION_STRING
  ) {
    // Production: Use Azure Application Insights
    registerOTel({
      serviceName,
      traceExporter: new AzureMonitorTraceExporter({
        connectionString: process.env.APPLICATIONINSIGHTS_CONNECTION_STRING,
      }),
    });
  } else if (isQA && process.env.GRAFANA_CLOUD_URL) {
    // QA: Use Grafana Cloud with custom exporter
    registerOTel({
      serviceName,
      // We'll handle traces through our custom metrics endpoint
      // since Grafana Cloud requires specific configuration
    });
  } else {
    // Development: Basic OpenTelemetry without external exporters
    registerOTel({
      serviceName,
    });
  }
}
