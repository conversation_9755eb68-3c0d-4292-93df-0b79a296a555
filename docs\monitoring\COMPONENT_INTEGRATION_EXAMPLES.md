# Component Integration Examples

This document shows how to integrate monitoring into your existing React components.

## Basic Component Monitoring

### Using the useMonitoring Hook

```typescript
import React, { useEffect } from 'react';
import { useMonitoring } from '@/hooks/use-monitoring';

const PatientListComponent: React.FC = () => {
  const { trackClick, trackEvent, trackError } = useMonitoring({
    componentName: 'PatientList',
    trackPageViews: true,
    trackUserInteractions: true
  });

  const handlePatientSelect = (patientId: string) => {
    trackClick('select_patient', { patientId });
    // Your existing logic here
  };

  const handleSearch = (query: string, results: any[]) => {
    trackEvent('patient_search', 1, {
      query_length: query.length.toString(),
      results_count: results.length.toString(),
      has_results: results.length > 0 ? 'true' : 'false'
    });
  };

  return (
    <div>
      {/* Your component JSX */}
    </div>
  );
};
```

### Using EMR-Specific Metrics

```typescript
import React from 'react';
import { trackPatientView, trackPatientSearch } from '@/core/lib/monitoring/emr-metrics';

const PatientDetailsComponent: React.FC<{ patientId: string }> = ({ patientId }) => {
  useEffect(() => {
    // Track patient view when component mounts
    trackPatientView(patientId, 'details');
  }, [patientId]);

  const handleTabChange = (tabName: string) => {
    trackPatientView(patientId, tabName as 'summary' | 'history');
  };

  return (
    <div>
      {/* Your component JSX */}
    </div>
  );
};
```

## Form Monitoring Example

```typescript
import React from 'react';
import { useForm } from 'react-hook-form';
import { useEMRMonitoring } from '@/hooks/use-monitoring';

const PrescriptionForm: React.FC = () => {
  const { trackFormSubmit, trackError } = useEMRMonitoring('PrescriptionForm');
  const { handleSubmit, formState: { errors } } = useForm();

  const onSubmit = async (data: any) => {
    const startTime = Date.now();
    
    try {
      // Your form submission logic
      await submitPrescription(data);
      
      const duration = Date.now() - startTime;
      trackFormSubmit('prescription_form', true, {
        medication_count: data.medications?.length || 0,
        submission_duration: duration
      });
      
    } catch (error) {
      trackFormSubmit('prescription_form', false, {
        error_message: error.message
      });
      trackError(error, { form: 'prescription_form' });
    }
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)}>
      {/* Your form JSX */}
    </form>
  );
};
```

## API Call Monitoring

```typescript
import React, { useEffect, useState } from 'react';
import { useMonitoring } from '@/hooks/use-monitoring';

const PatientDataComponent: React.FC = () => {
  const { trackApiLoading, trackError, trackEvent } = useMonitoring({
    componentName: 'PatientData'
  });
  const [loading, setLoading] = useState(false);

  const fetchPatientData = async (patientId: string) => {
    setLoading(true);
    trackApiLoading('fetch_patient_data', true);
    
    const startTime = Date.now();
    
    try {
      const data = await api.get(`/patients/${patientId}`);
      const duration = Date.now() - startTime;
      
      trackEvent('api_success', 1, {
        endpoint: 'fetch_patient_data',
        duration: duration.toString(),
        data_size: JSON.stringify(data).length.toString()
      });
      
      return data;
    } catch (error) {
      trackError(error, {
        endpoint: 'fetch_patient_data',
        patient_id: patientId
      });
      throw error;
    } finally {
      setLoading(false);
      trackApiLoading('fetch_patient_data', false);
    }
  };

  return (
    <div>
      {/* Your component JSX */}
    </div>
  );
};
```

## Modal/Dialog Monitoring

```typescript
import React, { useEffect } from 'react';
import { useMonitoring } from '@/hooks/use-monitoring';

const PatientModal: React.FC<{ isOpen: boolean; onClose: () => void }> = ({ 
  isOpen, 
  onClose 
}) => {
  const { trackModal } = useMonitoring({ componentName: 'PatientModal' });

  useEffect(() => {
    if (isOpen) {
      trackModal('patient_modal', 'open');
    }
  }, [isOpen, trackModal]);

  const handleClose = () => {
    trackModal('patient_modal', 'close');
    onClose();
  };

  return (
    <div>
      {/* Your modal JSX */}
    </div>
  );
};
```

## Dashboard Widget Monitoring

```typescript
import React, { useEffect } from 'react';
import { trackWidgetInteraction } from '@/core/lib/monitoring/emr-metrics';

const AppointmentWidget: React.FC = () => {
  useEffect(() => {
    // Track widget load
    trackWidgetInteraction('appointments_widget', 'load');
  }, []);

  const handleRefresh = () => {
    trackWidgetInteraction('appointments_widget', 'refresh');
    // Your refresh logic
  };

  const handleExpand = () => {
    trackWidgetInteraction('appointments_widget', 'expand');
    // Your expand logic
  };

  return (
    <div>
      {/* Your widget JSX */}
    </div>
  );
};
```

## Search Component Monitoring

```typescript
import React, { useState, useCallback } from 'react';
import { debounce } from 'lodash';
import { trackPatientSearch } from '@/core/lib/monitoring/emr-metrics';

const PatientSearch: React.FC = () => {
  const [query, setQuery] = useState('');
  const [results, setResults] = useState([]);

  const performSearch = useCallback(
    debounce(async (searchQuery: string) => {
      if (!searchQuery.trim()) return;

      const startTime = Date.now();
      
      try {
        const searchResults = await searchPatients(searchQuery);
        const duration = Date.now() - startTime;
        
        trackPatientSearch(searchQuery, searchResults.length, 'name');
        
        setResults(searchResults);
      } catch (error) {
        trackPatientSearch(searchQuery, 0, 'name');
      }
    }, 300),
    []
  );

  const handleInputChange = (value: string) => {
    setQuery(value);
    performSearch(value);
  };

  return (
    <div>
      {/* Your search component JSX */}
    </div>
  );
};
```

## Error Boundary with Monitoring

```typescript
import React, { Component, ReactNode } from 'react';
import { recordError } from '@/core/lib/monitoring/metrics';

interface Props {
  children: ReactNode;
  componentName: string;
}

interface State {
  hasError: boolean;
}

class MonitoredErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): State {
    return { hasError: true };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    recordError(error, {
      component: this.props.componentName,
      error_boundary: true,
      component_stack: errorInfo.componentStack,
      error_info: JSON.stringify(errorInfo)
    });
  }

  render() {
    if (this.state.hasError) {
      return <div>Something went wrong in {this.props.componentName}</div>;
    }

    return this.props.children;
  }
}

// Usage
const MyComponent = () => (
  <MonitoredErrorBoundary componentName="PatientList">
    <PatientListComponent />
  </MonitoredErrorBoundary>
);
```

## Performance Monitoring

```typescript
import React, { useEffect, useRef } from 'react';
import { trackEMRPerformance } from '@/core/lib/monitoring/emr-metrics';

const HeavyDataComponent: React.FC = () => {
  const renderStartTime = useRef<number>();

  useEffect(() => {
    renderStartTime.current = performance.now();
  }, []);

  useEffect(() => {
    if (renderStartTime.current) {
      const renderTime = performance.now() - renderStartTime.current;
      trackEMRPerformance('component_render', renderTime, {
        component: 'HeavyDataComponent'
      });
    }
  });

  const handleDataProcessing = async (data: any[]) => {
    const startTime = performance.now();
    
    // Your data processing logic
    const processedData = processData(data);
    
    const processingTime = performance.now() - startTime;
    trackEMRPerformance('data_processing', processingTime, {
      data_size: data.length.toString(),
      operation: 'process_patient_data'
    });
    
    return processedData;
  };

  return (
    <div>
      {/* Your component JSX */}
    </div>
  );
};
```

## Integration Checklist

When adding monitoring to components:

1. **Import the appropriate monitoring functions**
2. **Track component lifecycle events** (mount, unmount, time spent)
3. **Track user interactions** (clicks, form submissions, searches)
4. **Track API calls and their performance**
5. **Track errors with context**
6. **Track business-specific metrics** (patient views, prescriptions, etc.)
7. **Use meaningful labels and metadata**
8. **Don't over-monitor** - focus on important user actions and performance metrics

## Best Practices

- **Use descriptive metric names** that clearly indicate what's being measured
- **Include relevant context** in labels (patient ID, component name, etc.)
- **Track both success and failure cases**
- **Monitor performance-critical operations**
- **Use debouncing for frequent events** like search input
- **Batch related metrics** when possible
- **Test monitoring in development** before deploying to QA
