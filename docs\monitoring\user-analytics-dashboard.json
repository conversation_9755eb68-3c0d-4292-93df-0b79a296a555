{"dashboard": {"id": null, "title": "ARCA EMR - User Analytics & Behavior", "tags": ["arca-emr", "analytics", "user-behavior", "qa"], "timezone": "browser", "refresh": "1m", "time": {"from": "now-24h", "to": "now"}, "panels": [{"id": 1, "title": "User Activity Overview", "type": "stat", "targets": [{"expr": "count(count by (session_id)(arca_emr_page_view))", "legendFormat": "Active Sessions"}, {"expr": "sum(increase(arca_emr_page_view[1h]))", "legendFormat": "Page Views (1h)"}, {"expr": "sum(increase(arca_emr_user_interaction[1h]))", "legendFormat": "User Interactions (1h)"}], "gridPos": {"h": 6, "w": 24, "x": 0, "y": 0}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "unit": "short"}}}, {"id": 2, "title": "Page Views by URL", "type": "timeseries", "targets": [{"expr": "sum(rate(arca_emr_page_view[5m])) by (url)", "legendFormat": "{{url}}"}], "gridPos": {"h": 8, "w": 12, "x": 0, "y": 6}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "unit": "reqps"}}}, {"id": 3, "title": "User Interaction Types", "type": "piechart", "targets": [{"expr": "sum(increase(arca_emr_user_interaction[1h])) by (action)", "legendFormat": "{{action}}"}], "gridPos": {"h": 8, "w": 12, "x": 12, "y": 6}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "unit": "short"}}}, {"id": 4, "title": "Session Duration Distribution", "type": "histogram", "targets": [{"expr": "histogram_quantile(0.95, sum(rate(arca_emr_session_duration_bucket[5m])))", "legendFormat": "95th percentile"}, {"expr": "histogram_quantile(0.50, sum(rate(arca_emr_session_duration_bucket[5m])))", "legendFormat": "50th percentile"}], "gridPos": {"h": 8, "w": 12, "x": 0, "y": 14}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "unit": "s"}}}, {"id": 5, "title": "Most Active Components", "type": "table", "targets": [{"expr": "topk(10, sum(increase(arca_emr_user_interaction[1h])) by (component))", "format": "table", "instant": true}], "gridPos": {"h": 8, "w": 12, "x": 12, "y": 14}, "transformations": [{"id": "organize", "options": {"excludeByName": {"Time": true}, "renameByName": {"component": "Component", "Value": "Interactions"}}}]}, {"id": 6, "title": "User Journey Flow", "type": "timeseries", "targets": [{"expr": "sum(rate(arca_emr_page_navigation[5m])) by (to_path)", "legendFormat": "To: {{to_path}}"}], "gridPos": {"h": 8, "w": 12, "x": 0, "y": 22}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "unit": "reqps"}}}, {"id": 7, "title": "Search Activity", "type": "timeseries", "targets": [{"expr": "sum(rate(arca_emr_patient_search[5m])) by (search_type)", "legendFormat": "Patient Search: {{search_type}}"}, {"expr": "sum(rate(arca_emr_search_performed[5m]))", "legendFormat": "General Search"}], "gridPos": {"h": 8, "w": 12, "x": 12, "y": 22}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "unit": "reqps"}}}, {"id": 8, "title": "Form Submission Success Rate", "type": "stat", "targets": [{"expr": "sum(rate(arca_emr_form_submission{success=\"true\"}[5m])) / sum(rate(arca_emr_form_submission[5m])) * 100", "legendFormat": "Success Rate %"}], "gridPos": {"h": 6, "w": 12, "x": 0, "y": 30}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "unit": "percent", "min": 0, "max": 100, "thresholds": {"steps": [{"color": "red", "value": null}, {"color": "yellow", "value": 90}, {"color": "green", "value": 95}]}}}}, {"id": 9, "title": "<PERSON><PERSON> Click Heatmap", "type": "table", "targets": [{"expr": "topk(15, sum(increase(arca_emr_button_click[1h])) by (button, component))", "format": "table", "instant": true}], "gridPos": {"h": 6, "w": 12, "x": 12, "y": 30}, "transformations": [{"id": "organize", "options": {"excludeByName": {"Time": true}, "renameByName": {"button": "<PERSON><PERSON>", "component": "Component", "Value": "<PERSON>licks"}}}]}, {"id": 10, "title": "Modal Interaction Patterns", "type": "timeseries", "targets": [{"expr": "sum(rate(arca_emr_modal_open[5m])) by (modal)", "legendFormat": "Opened: {{modal}}"}, {"expr": "sum(rate(arca_emr_modal_close[5m])) by (modal)", "legendFormat": "Closed: {{modal}}"}], "gridPos": {"h": 8, "w": 12, "x": 0, "y": 36}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "unit": "reqps"}}}, {"id": 11, "title": "User Engagement Score", "type": "gauge", "targets": [{"expr": "(sum(rate(arca_emr_user_interaction[5m])) / count(count by (session_id)(arca_emr_page_view))) * 100", "legendFormat": "Interactions per Session"}], "gridPos": {"h": 8, "w": 12, "x": 12, "y": 36}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "unit": "short", "min": 0, "max": 50, "thresholds": {"steps": [{"color": "red", "value": null}, {"color": "yellow", "value": 10}, {"color": "green", "value": 20}]}}}}, {"id": 12, "title": "Time Spent on Components", "type": "table", "targets": [{"expr": "topk(10, avg(arca_emr_component_time_spent) by (component_name) / 1000)", "format": "table", "instant": true}], "gridPos": {"h": 8, "w": 12, "x": 0, "y": 44}, "transformations": [{"id": "organize", "options": {"excludeByName": {"Time": true}, "renameByName": {"component_name": "Component", "Value": "Avg Time (seconds)"}}}]}, {"id": 13, "title": "Entry Points Analysis", "type": "piechart", "targets": [{"expr": "sum(increase(arca_emr_session_start[24h])) by (entry_point)", "legendFormat": "{{entry_point}}"}], "gridPos": {"h": 8, "w": 12, "x": 12, "y": 44}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "unit": "short"}}}]}}