/**
 * Metrics Collection API Endpoint
 * Collects frontend metrics and forwards them to Grafana Cloud
 */

import { NextRequest, NextResponse } from 'next/server';

interface MetricPayload {
  metrics: Array<{
    name: string;
    value: number;
    timestamp: number;
    labels?: Record<string, string>;
    environment?: string;
  }>;
  performance: Array<{
    name: string;
    value: number;
    timestamp: number;
    url: string;
    environment: string;
  }>;
  interactions: Array<{
    action: string;
    component: string;
    url: string;
    timestamp: number;
    userId?: string;
    sessionId: string;
    environment: string;
    metadata?: Record<string, any>;
  }>;
  timestamp: number;
  environment: string;
  sessionId: string;
  userId?: string;
  url: string;
}

interface ErrorPayload {
  errors: Array<{
    error: string;
    stack?: string;
    url: string;
    userAgent: string;
    timestamp: number;
    userId?: string;
    sessionId: string;
    environment: string;
  }>;
  timestamp: number;
  environment: string;
  sessionId: string;
  userId?: string;
}

// Convert metrics to Prometheus format for Grafana
const convertToPrometheusFormat = (payload: MetricPayload): string => {
  const lines: string[] = [];
  const timestamp = Math.floor(payload.timestamp / 1000); // Convert to seconds

  // Process regular metrics
  payload.metrics.forEach((metric) => {
    const labels = {
      environment: payload.environment,
      session_id: payload.sessionId,
      ...(payload.userId && { user_id: payload.userId }),
      ...metric.labels,
    };

    const labelString = Object.entries(labels)
      .map(([key, value]) => `${key}="${value}"`)
      .join(',');

    lines.push(
      `arca_emr_${metric.name}{${labelString}} ${metric.value} ${Math.floor(metric.timestamp / 1000)}`
    );
  });

  // Process performance metrics
  payload.performance.forEach((perf) => {
    const labels = {
      environment: payload.environment,
      url: perf.url,
      metric_type: 'performance',
    };

    const labelString = Object.entries(labels)
      .map(([key, value]) => `${key}="${value}"`)
      .join(',');

    lines.push(
      `arca_emr_performance_${perf.name}{${labelString}} ${perf.value} ${Math.floor(perf.timestamp / 1000)}`
    );
  });

  // Process user interactions
  payload.interactions.forEach((interaction) => {
    const labels = {
      environment: payload.environment,
      component: interaction.component,
      action: interaction.action,
      url: interaction.url,
      session_id: interaction.sessionId,
      ...(interaction.userId && { user_id: interaction.userId }),
    };

    const labelString = Object.entries(labels)
      .map(([key, value]) => `${key}="${value}"`)
      .join(',');

    lines.push(
      `arca_emr_user_interaction{${labelString}} 1 ${Math.floor(interaction.timestamp / 1000)}`
    );
  });

  return lines.join('\n');
};

// Convert errors to structured logs for Grafana Loki
const convertErrorsToLoki = (payload: ErrorPayload) => {
  return {
    streams: payload.errors.map((error) => ({
      stream: {
        environment: payload.environment,
        level: 'error',
        source: 'frontend',
        session_id: payload.sessionId,
        ...(payload.userId && { user_id: payload.userId }),
      },
      values: [
        [
          (error.timestamp * 1000000).toString(), // Nanoseconds
          JSON.stringify({
            message: error.error,
            stack: error.stack,
            url: error.url,
            userAgent: error.userAgent,
            timestamp: error.timestamp,
            environment: payload.environment,
          }),
        ],
      ],
    })),
  };
};

// Send metrics to Grafana Cloud
async function sendToGrafanaCloud(data: string, type: 'metrics' | 'logs') {
  const grafanaUrl = process.env.GRAFANA_CLOUD_URL;
  const grafanaUser = process.env.GRAFANA_CLOUD_USER;
  const grafanaApiKey = process.env.GRAFANA_CLOUD_API_KEY;

  if (!grafanaUrl || !grafanaUser || !grafanaApiKey) {
    console.log('Grafana Cloud not configured, logging metrics locally');
    console.log('Metrics data:', data);
    return;
  }

  try {
    const endpoint =
      type === 'metrics'
        ? `${grafanaUrl}/api/prom/push`
        : `${grafanaUrl}/loki/api/v1/push`;

    const headers = {
      Authorization: `Bearer ${grafanaUser}:${grafanaApiKey}`,
      'Content-Type': type === 'metrics' ? 'text/plain' : 'application/json',
    };

    const response = await fetch(endpoint, {
      method: 'POST',
      headers,
      body: data,
    });

    if (!response.ok) {
      console.error(`Failed to send ${type} to Grafana:`, response.statusText);
    }
  } catch (error) {
    console.error(`Error sending ${type} to Grafana:`, error);
  }
}

// Send traces to Grafana Tempo
async function sendTracesToGrafana(payload: any) {
  const tempoUrl = process.env.GRAFANA_TEMPO_URL;
  const grafanaUser = process.env.GRAFANA_CLOUD_USER;
  const grafanaApiKey = process.env.GRAFANA_CLOUD_API_KEY;

  if (!tempoUrl || !grafanaUser || !grafanaApiKey) {
    console.log('Tempo not configured, skipping trace export');
    return;
  }

  try {
    const response = await fetch(`${tempoUrl}/api/traces`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Basic ${Buffer.from(`${grafanaUser}:${grafanaApiKey}`).toString('base64')}`,
      },
      body: JSON.stringify(payload.traces),
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    console.log('Traces sent to Grafana Tempo successfully');
  } catch (error) {
    console.error('Error sending traces to Grafana Tempo:', error);
  }
}

// Handle metrics and traces collection
export async function POST(request: NextRequest) {
  try {
    const url = new URL(request.url);
    const pathname = url.pathname;

    if (pathname.endsWith('/traces')) {
      const payload = await request.json();

      // Send traces to Grafana Tempo (if configured)
      await sendTracesToGrafana(payload);

      return NextResponse.json({
        success: true,
        tracesCount: payload.traces?.data?.length || 0,
      });
    }

    if (pathname.endsWith('/metrics')) {
      const payload: MetricPayload = await request.json();

      // Validate payload
      if (!payload.metrics && !payload.performance && !payload.interactions) {
        return NextResponse.json(
          { error: 'No metrics data provided' },
          { status: 400 }
        );
      }

      // Convert to Prometheus format
      const prometheusData = convertToPrometheusFormat(payload);

      // Send to Grafana Cloud
      await sendToGrafanaCloud(prometheusData, 'metrics');

      return NextResponse.json({
        success: true,
        metricsCount: payload.metrics?.length || 0,
      });
    }

    if (pathname.endsWith('/errors')) {
      const payload: ErrorPayload = await request.json();

      // Validate payload
      if (!payload.errors || payload.errors.length === 0) {
        return NextResponse.json(
          { error: 'No error data provided' },
          { status: 400 }
        );
      }

      // Convert to Loki format
      const lokiData = convertErrorsToLoki(payload);

      // Send to Grafana Cloud
      await sendToGrafanaCloud(JSON.stringify(lokiData), 'logs');

      return NextResponse.json({
        success: true,
        errorsCount: payload.errors.length,
      });
    }

    return NextResponse.json({ error: 'Invalid endpoint' }, { status: 404 });
  } catch (error) {
    console.error('Error processing metrics:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// Health check endpoint
export async function GET() {
  return NextResponse.json({
    status: 'healthy',
    timestamp: Date.now(),
    environment: process.env.NEXT_PUBLIC_NODE_ENV || 'development',
  });
}
