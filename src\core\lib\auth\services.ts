import { SilentRequest } from '@azure/msal-browser';
import { toast } from 'sonner';

import { useCurrentPatientStore } from '@/store/currentPatientStore';
import { useDoctorStore } from '@/store/emr/doctor-profile/personal-info';
import { useTestReportFilterStore } from '@/store/emr/lab/test-report-filter';

import API_CONFIG from '@/core/configs/api';

import { msalInstance, Scope } from './msal';
import {
  trackUserLogin,
  trackUserLogout,
} from '@/core/lib/monitoring/emr-metrics';
import { setUserId } from '@/core/lib/monitoring/metrics';

export const appURL = API_CONFIG.APP_BASE_URL;

export async function login() {
  const loginStartTime = Date.now();

  try {
    useDoctorStore.getState().clearStorage();
    useCurrentPatientStore.getState().clear();
    useTestReportFilterStore.getState().resetState();
    localStorage?.clear();

    await msalInstance.initialize();

    const res = await msalInstance.loginPopup({
      scopes: [Scope.ApiRead],
    });

    // Track successful login
    if (res?.account) {
      const userId = res.account.homeAccountId || res.account.localAccountId;
      setUserId(userId);
      trackUserLogin(userId, 'popup');
    }

    toast.success(`Welcome ${res?.account?.name}`);
  } catch (error) {
    // Track failed login
    const loginDuration = Date.now() - loginStartTime;
    trackUserLogin('unknown', 'popup'); // Still track the attempt

    console.error(error);
    toast.error('Login failed');
  }
}

export async function logout() {
  const sessionStartTime = performance.timeOrigin;
  const sessionDuration = Date.now() - sessionStartTime;

  try {
    const activeAccount = msalInstance.getActiveAccount();
    const userId =
      activeAccount?.homeAccountId ||
      activeAccount?.localAccountId ||
      'unknown';

    await msalInstance.initialize();
    await msalInstance.logoutPopup({
      account: activeAccount,
    });

    // Track logout before clearing storage
    trackUserLogout(userId, sessionDuration);

    useDoctorStore.getState().clearStorage();
    useCurrentPatientStore.getState().clear();
    useTestReportFilterStore.getState().resetState();
    localStorage?.clear();
    window.location.href = '/login';
  } catch (error) {
    console.error(error);
    // toast.error('Logout failed');
  }
}

export async function getToken() {
  await msalInstance.initialize();

  const accounts = msalInstance.getAllAccounts();

  if (accounts.length === 0) {
    await logout();
    window.location.href = '/login';
    throw new Error('No accounts found');
  }

  try {
    const silentTokenRequest: SilentRequest = {
      scopes: [Scope.ApiRead],
      account: accounts[0],
    };

    const tokenResponse =
      await msalInstance.acquireTokenSilent(silentTokenRequest);

    if (!tokenResponse?.idToken) {
      await logout();
      window.location.href = '/login';
      throw new Error('Token retrieval failed');
    }

    return tokenResponse.idToken;
  } catch (error) {
    console.error('Error acquiring token:', error);
    await logout();
    window.location.href = '/login';
    throw new Error('Failed to acquire token');
  }
}

export function getActiveAccount() {
  try {
    const account = msalInstance.getActiveAccount();

    return account || null;
  } catch (_error) {
    console.error('Error getting active account:', _error);
    return null;
  }
}

export async function setFirstAccountAsActive() {
  try {
    const accounts = msalInstance.getAllAccounts();

    if (accounts.length) {
      msalInstance.setActiveAccount(accounts[0]);
    }
  } catch (error) {
    console.error(error);
    toast.error('Error setting active account');
  }
}
