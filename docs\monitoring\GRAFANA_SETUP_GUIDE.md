# Grafana Cloud Setup Guide for ARCA EMR QA Environment

This guide will help you set up comprehensive monitoring for your QA environment using Grafana Cloud.

## Prerequisites

- Vercel account with your QA deployment
- Access to environment variables in Vercel
- Basic understanding of metrics and monitoring

## Step 1: Create Grafana Cloud Account

1. **Sign up for Grafana Cloud**:
   - Visit https://grafana.com/products/cloud/
   - Click "Start for free" (Free tier includes 10k metrics, 50GB logs)
   - Complete the registration process

2. **Access your Grafana Cloud instance**:
   - After signup, you'll get a Grafana Cloud URL like: `https://yourorg.grafana.net`
   - Note down your organization name

## Step 2: Configure Data Sources

### 2.1 Prometheus (Metrics)

1. In Grafana Cloud, go to **Connections** → **Data Sources**
2. Your Prometheus data source should be pre-configured
3. Note the **Remote Write URL** (looks like: `https://prometheus-prod-XX-XX-X.grafana.net/api/prom/push`)
4. Note your **User ID** and **API Key** from the data source configuration

### 2.2 Loki (Logs)

1. In Grafana Cloud, Loki should also be pre-configured
2. Note the **Loki URL** (looks like: `https://logs-prod-XX-XX-X.grafana.net`)

## Step 3: Configure Vercel Environment Variables

1. Go to your Vercel dashboard
2. Select your ARCA EMR project
3. Go to **Settings** → **Environment Variables**
4. Add the following variables for **Production** environment (your QA):

```bash
# Monitoring Configuration
NEXT_PUBLIC_METRICS_ENDPOINT=https://your-qa-domain.vercel.app/api
GRAFANA_CLOUD_URL=https://prometheus-prod-XX-XX-X.grafana.net
GRAFANA_CLOUD_USER=your-user-id
GRAFANA_CLOUD_API_KEY=your-api-key
GRAFANA_LOKI_URL=https://logs-prod-XX-XX-X.grafana.net
```

**Important**: Replace the placeholder values with your actual Grafana Cloud credentials.

## Step 4: Deploy Updated Code

1. Commit and push your monitoring code changes
2. Vercel will automatically deploy with the new environment variables
3. Verify the deployment is successful

## Step 5: Import Dashboard

### Option A: Manual Import

1. In Grafana Cloud, go to **Dashboards** → **Import**
2. Copy the JSON from `docs/monitoring/grafana-dashboard-config.json`
3. Paste it into the import dialog
4. Click **Load** and then **Import**

### Option B: Create Custom Dashboard

1. Go to **Dashboards** → **New** → **New Dashboard**
2. Add panels with these queries:

#### Key Metrics Panels:

**Page Views per Minute**:
```promql
sum(rate(arca_emr_page_view[5m]))
```

**API Response Times (95th percentile)**:
```promql
histogram_quantile(0.95, rate(arca_emr_api_call_duration_bucket[5m]))
```

**Error Rate**:
```promql
sum(rate(arca_emr_frontend_error_count[5m]))
```

**Active Sessions**:
```promql
count(count by (session_id)(arca_emr_page_view))
```

## Step 6: Set Up Alerts

1. Go to **Alerting** → **Alert Rules**
2. Create alerts for:

### High Error Rate Alert:
```yaml
Query: sum(rate(arca_emr_frontend_error_count[5m])) > 0.1
Condition: IS ABOVE 0.1
Evaluation: Every 1m for 2m
```

### Slow API Response Alert:
```yaml
Query: histogram_quantile(0.95, rate(arca_emr_api_call_duration_bucket[5m])) > 5000
Condition: IS ABOVE 5000 (5 seconds)
Evaluation: Every 1m for 3m
```

### Low Page Views (Possible Outage):
```yaml
Query: sum(rate(arca_emr_page_view[5m])) < 0.01
Condition: IS BELOW 0.01
Evaluation: Every 1m for 5m
```

## Step 7: Configure Notification Channels

1. Go to **Alerting** → **Contact Points**
2. Add notification channels:
   - **Email**: Add your team's email addresses
   - **Slack**: Connect your Slack workspace (optional)
   - **Webhook**: For custom integrations (optional)

## Step 8: Test Your Setup

1. **Generate Test Traffic**:
   - Visit your QA application: https://arca-emr.vercel.app
   - Navigate through different pages
   - Perform some actions (login, view patients, etc.)

2. **Check Metrics in Grafana**:
   - Go to your dashboard
   - You should see metrics appearing within 1-2 minutes
   - If no data appears, check the browser console for errors

3. **Test Error Tracking**:
   - Open browser console and run: `throw new Error("Test error")`
   - Check if the error appears in your logs

## Step 9: Monitor Key EMR Metrics

Your monitoring setup will track:

### Performance Metrics:
- Page load times
- API response times
- Core Web Vitals (LCP, FID, CLS)
- Component render times

### User Behavior:
- Page views and navigation patterns
- Button clicks and form submissions
- Search queries and results
- Modal interactions

### Medical-Specific Actions:
- Patient record access
- Consultation activities
- Prescription management
- Medical record updates

### Error Tracking:
- JavaScript errors
- API failures
- Authentication issues
- Network problems

## Troubleshooting

### No Metrics Appearing:

1. **Check Environment Variables**:
   ```bash
   # In Vercel, verify these are set correctly:
   GRAFANA_CLOUD_URL
   GRAFANA_CLOUD_USER  
   GRAFANA_CLOUD_API_KEY
   ```

2. **Check API Endpoint**:
   - Visit: `https://your-qa-domain.vercel.app/api/metrics`
   - Should return: `{"status":"healthy","timestamp":...}`

3. **Check Browser Console**:
   - Look for any JavaScript errors
   - Check if metrics are being sent (Network tab)

### Metrics Not Reaching Grafana:

1. **Verify Credentials**:
   - Test your Grafana Cloud API key
   - Ensure the Prometheus URL is correct

2. **Check Logs**:
   - In Vercel, check function logs for the `/api/metrics` endpoint
   - Look for any error messages

### Dashboard Not Showing Data:

1. **Check Time Range**: Ensure you're looking at recent data
2. **Verify Queries**: Test individual Prometheus queries
3. **Check Data Source**: Ensure Prometheus is connected

## Next Steps

Once monitoring is working:

1. **Customize Dashboards**: Add panels specific to your EMR workflows
2. **Set Up More Alerts**: Create alerts for business-critical metrics
3. **Monitor Trends**: Use data to identify performance improvements
4. **Scale Monitoring**: Consider upgrading Grafana Cloud plan if needed

## Support

- **Grafana Documentation**: https://grafana.com/docs/
- **Prometheus Queries**: https://prometheus.io/docs/prometheus/latest/querying/
- **Community Support**: https://community.grafana.com/
