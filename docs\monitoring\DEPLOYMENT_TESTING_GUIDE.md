# Deployment and Testing Guide for ARCA EMR Monitoring

This guide covers deploying the monitoring system to your QA environment and validating that everything works correctly.

## Pre-Deployment Checklist

### Code Changes Verification

1. **Monitoring Infrastructure**:
   - [ ] `src/core/lib/monitoring/metrics.ts` - Core metrics collection
   - [ ] `src/core/lib/monitoring/api-interceptor.ts` - API monitoring
   - [ ] `src/core/lib/monitoring/emr-metrics.ts` - EMR-specific metrics
   - [ ] `src/hooks/use-monitoring.ts` - React monitoring hooks
   - [ ] `src/core/providers/MonitoringProvider.tsx` - Monitoring provider
   - [ ] `src/app/api/metrics/route.ts` - Metrics API endpoint

2. **Integration Points**:
   - [ ] Updated `src/core/lib/interceptor/index.ts` with monitoring
   - [ ] Updated `src/core/lib/auth/services.ts` with user tracking
   - [ ] Updated `src/core/providers/AppProviders.tsx` with MonitoringProvider

3. **Configuration Files**:
   - [ ] Updated `.env` with monitoring environment variables
   - [ ] Dashboard JSON files in `docs/monitoring/`
   - [ ] Alert configuration files

### Environment Variables Setup

1. **In Vercel Dashboard**:
   - Go to your project settings
   - Navigate to "Environment Variables"
   - Add these variables for **Production** environment (your QA):

```bash
# Required for QA monitoring
NEXT_PUBLIC_METRICS_ENDPOINT=https://arca-emr.vercel.app/api
GRAFANA_CLOUD_URL=https://prometheus-prod-XX-XX-X.grafana.net
GRAFANA_CLOUD_USER=your-user-id
GRAFANA_CLOUD_API_KEY=your-api-key
GRAFANA_LOKI_URL=https://logs-prod-XX-XX-X.grafana.net

# Optional: Enable monitoring in development
NEXT_PUBLIC_ENABLE_DEV_MONITORING=false
```

## Deployment Steps

### Step 1: Deploy to Vercel

1. **Commit and Push Changes**:
   ```bash
   git add .
   git commit -m "Add comprehensive monitoring system for QA environment"
   git push origin main
   ```

2. **Verify Deployment**:
   - Check Vercel dashboard for successful deployment
   - Verify no build errors
   - Check function logs for any startup issues

3. **Test Basic Functionality**:
   - Visit your QA URL: `https://arca-emr.vercel.app`
   - Verify the application loads correctly
   - Check browser console for any JavaScript errors

### Step 2: Validate Metrics Endpoint

1. **Test Health Check**:
   ```bash
   curl https://arca-emr.vercel.app/api/metrics
   ```
   Expected response:
   ```json
   {
     "status": "healthy",
     "timestamp": **********,
     "environment": "production"
   }
   ```

2. **Check Browser Network Tab**:
   - Open browser developer tools
   - Navigate through your application
   - Look for POST requests to `/api/metrics`
   - Verify metrics are being sent (should see requests every 30 seconds)

### Step 3: Verify Data Flow to Grafana

1. **Generate Test Traffic**:
   - Navigate through different pages
   - Perform various actions (login, search, form submissions)
   - Generate some errors (try invalid API calls)
   - Use the application for 5-10 minutes

2. **Check Grafana Data Sources**:
   - Go to Grafana Cloud
   - Navigate to **Explore**
   - Select your Prometheus data source
   - Test these queries:
   ```promql
   # Check if metrics are arriving
   arca_emr_page_view
   
   # Check API metrics
   arca_emr_api_call_count
   
   # Check error metrics
   arca_emr_frontend_error_count
   ```

3. **Verify Logs in Loki** (if configured):
   - Switch to Loki data source in Explore
   - Query: `{environment="production"}`
   - Should see error logs if any errors occurred

## Testing Scenarios

### Scenario 1: Basic User Journey

1. **Actions to Perform**:
   - Visit homepage
   - Login to the application
   - Navigate to patient list
   - Search for a patient
   - View patient details
   - Logout

2. **Expected Metrics**:
   - Page views for each navigation
   - User login/logout events
   - Search interactions
   - API calls for data fetching
   - Session duration tracking

3. **Verification**:
   ```promql
   # Check page views
   sum(increase(arca_emr_page_view[5m]))
   
   # Check user interactions
   sum(increase(arca_emr_user_interaction[5m])) by (action)
   
   # Check API calls
   sum(increase(arca_emr_api_call_count[5m])) by (endpoint)
   ```

### Scenario 2: Error Generation

1. **Generate Frontend Errors**:
   - Open browser console
   - Run: `throw new Error("Test monitoring error")`
   - Try to access non-existent pages
   - Submit forms with invalid data

2. **Generate API Errors**:
   - Try to access protected endpoints without authentication
   - Make requests to non-existent API endpoints
   - Submit malformed data to APIs

3. **Expected Results**:
   - Error metrics should increase
   - Error logs should appear in Loki
   - Error details should be captured with context

4. **Verification**:
   ```promql
   # Check error rates
   sum(rate(arca_emr_frontend_error_count[5m]))
   
   # Check API error rates
   sum(rate(arca_emr_api_call_count{success="false"}[5m]))
   ```

### Scenario 3: Performance Testing

1. **Generate Load**:
   - Open multiple browser tabs
   - Navigate quickly between pages
   - Perform rapid searches
   - Submit multiple forms

2. **Expected Metrics**:
   - API response time metrics
   - Core Web Vitals data
   - Component performance metrics
   - Page load time measurements

3. **Verification**:
   ```promql
   # Check response times
   histogram_quantile(0.95, rate(arca_emr_api_call_duration_bucket[5m]))
   
   # Check Core Web Vitals
   avg(arca_emr_performance_largest_contentful_paint)
   ```

## Dashboard Validation

### Import and Configure Dashboards

1. **Import Main Dashboard**:
   - Copy JSON from `docs/monitoring/grafana-dashboard-config.json`
   - Import in Grafana Cloud
   - Verify all panels show data

2. **Import Specialized Dashboards**:
   - Business metrics dashboard
   - Performance and error dashboard
   - User analytics dashboard

3. **Verify Dashboard Functionality**:
   - All panels should show data within 5-10 minutes
   - Time range selectors should work
   - Refresh functionality should update data
   - Drill-down links should work

### Dashboard Testing Checklist

- [ ] **Application Overview Panel**: Shows page views, API calls, errors
- [ ] **API Response Times**: Shows percentile data
- [ ] **Error Rate Panels**: Shows error trends
- [ ] **Core Web Vitals**: Shows performance metrics
- [ ] **User Interactions**: Shows user behavior data
- [ ] **Business Metrics**: Shows EMR-specific actions
- [ ] **Performance Metrics**: Shows component and operation performance

## Alert Testing

### Test Alert Configuration

1. **Create Test Alerts**:
   - Set very low thresholds temporarily
   - Generate activity to trigger alerts
   - Verify notifications are received

2. **Test Contact Points**:
   - Send test notifications from Grafana
   - Verify email delivery
   - Check Slack notifications (if configured)

3. **Validate Alert Content**:
   - Check alert messages are clear
   - Verify runbook links work
   - Confirm severity levels are correct

### Alert Testing Scenarios

1. **High Error Rate Alert**:
   - Generate multiple errors quickly
   - Should trigger within 2 minutes
   - Should receive email/Slack notification

2. **Performance Alert**:
   - Simulate slow responses (if possible)
   - Should trigger after sustained poor performance
   - Should include performance context

## Troubleshooting Common Issues

### No Metrics Appearing

1. **Check Environment Variables**:
   ```bash
   # In Vercel function logs, verify these are set:
   GRAFANA_CLOUD_URL
   GRAFANA_CLOUD_USER
   GRAFANA_CLOUD_API_KEY
   ```

2. **Check API Endpoint**:
   - Visit `/api/metrics` directly
   - Should return health check response
   - Check Vercel function logs for errors

3. **Verify Browser Metrics**:
   - Open browser developer tools
   - Check Network tab for `/api/metrics` requests
   - Look for any JavaScript errors in console

### Metrics Not Reaching Grafana

1. **Test Grafana Credentials**:
   - Verify API key has correct permissions
   - Test connection from Grafana data sources page
   - Check Grafana Cloud usage limits

2. **Check Prometheus Format**:
   - Verify metrics are in correct Prometheus format
   - Check for any formatting errors in logs
   - Validate metric names and labels

### Dashboard Issues

1. **No Data in Panels**:
   - Check time range (try "Last 24 hours")
   - Verify data source is selected correctly
   - Test individual queries in Explore

2. **Query Errors**:
   - Check Prometheus query syntax
   - Verify metric names match exactly
   - Test queries in Grafana Explore

## Performance Optimization

### Monitoring Overhead

1. **Monitor Resource Usage**:
   - Check Vercel function execution time
   - Monitor memory usage
   - Track API endpoint performance

2. **Optimize Metrics Collection**:
   - Adjust flush intervals if needed
   - Reduce metric granularity for high-volume events
   - Use sampling for very frequent events

### Grafana Cloud Limits

1. **Monitor Usage**:
   - Check metrics ingestion rate
   - Monitor log volume
   - Track query performance

2. **Optimize if Needed**:
   - Reduce metric cardinality
   - Adjust retention periods
   - Use recording rules for expensive queries

## Success Criteria

Your monitoring system is successfully deployed when:

- [ ] **Metrics Flow**: Data appears in Grafana within 2 minutes of activity
- [ ] **Dashboards Work**: All imported dashboards show relevant data
- [ ] **Alerts Function**: Test alerts trigger and send notifications
- [ ] **Performance Impact**: Application performance is not noticeably affected
- [ ] **Error Tracking**: Errors are captured and visible in dashboards
- [ ] **User Analytics**: User interactions are tracked and visible
- [ ] **API Monitoring**: API calls are monitored with response times and error rates

## Next Steps

After successful deployment:

1. **Monitor for 24-48 hours** to establish baseline metrics
2. **Adjust alert thresholds** based on normal operational patterns
3. **Train team members** on using dashboards and responding to alerts
4. **Document runbooks** for common alert scenarios
5. **Plan regular reviews** of monitoring effectiveness

## Support and Maintenance

### Regular Tasks

1. **Weekly**: Review alert effectiveness and false positive rates
2. **Monthly**: Analyze trends and optimize dashboards
3. **Quarterly**: Review and update monitoring strategy

### Getting Help

- **Grafana Documentation**: https://grafana.com/docs/
- **Prometheus Query Help**: https://prometheus.io/docs/prometheus/latest/querying/
- **Community Support**: https://community.grafana.com/

Your ARCA EMR monitoring system is now ready for production use in your QA environment!
