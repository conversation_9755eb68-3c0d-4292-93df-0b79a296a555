{"dashboard": {"id": null, "title": "ARCA EMR - QA Environment Monitoring", "tags": ["arca-emr", "qa", "frontend", "monitoring"], "timezone": "browser", "refresh": "30s", "time": {"from": "now-1h", "to": "now"}, "panels": [{"id": 1, "title": "Application Overview", "type": "stat", "targets": [{"expr": "sum(rate(arca_emr_page_view[5m]))", "legendFormat": "Page Views/min"}, {"expr": "sum(rate(arca_emr_api_call_count[5m]))", "legendFormat": "API Calls/min"}, {"expr": "sum(rate(arca_emr_frontend_error_count[5m]))", "legendFormat": "Errors/min"}], "gridPos": {"h": 8, "w": 24, "x": 0, "y": 0}}, {"id": 2, "title": "API Response Times", "type": "graph", "targets": [{"expr": "histogram_quantile(0.95, rate(arca_emr_api_call_duration_bucket[5m]))", "legendFormat": "95th percentile"}, {"expr": "histogram_quantile(0.50, rate(arca_emr_api_call_duration_bucket[5m]))", "legendFormat": "50th percentile"}], "yAxes": [{"label": "Response Time (ms)", "min": 0}], "gridPos": {"h": 8, "w": 12, "x": 0, "y": 8}}, {"id": 3, "title": "Error Rate by Endpoint", "type": "graph", "targets": [{"expr": "sum(rate(arca_emr_api_call_count{success=\"false\"}[5m])) by (endpoint)", "legendFormat": "{{endpoint}}"}], "yAxes": [{"label": "Errors/min", "min": 0}], "gridPos": {"h": 8, "w": 12, "x": 12, "y": 8}}, {"id": 4, "title": "Core Web Vitals", "type": "graph", "targets": [{"expr": "avg(arca_emr_performance_largest_contentful_paint)", "legendFormat": "LCP (ms)"}, {"expr": "avg(arca_emr_performance_first_input_delay)", "legendFormat": "FID (ms)"}, {"expr": "avg(arca_emr_performance_cumulative_layout_shift)", "legendFormat": "CLS"}], "gridPos": {"h": 8, "w": 12, "x": 0, "y": 16}}, {"id": 5, "title": "User Interactions", "type": "graph", "targets": [{"expr": "sum(rate(arca_emr_user_interaction[5m])) by (action)", "legendFormat": "{{action}}"}], "gridPos": {"h": 8, "w": 12, "x": 12, "y": 16}}, {"id": 6, "title": "Active Users", "type": "stat", "targets": [{"expr": "count(count by (session_id)(arca_emr_page_view))", "legendFormat": "Active Sessions"}], "gridPos": {"h": 4, "w": 6, "x": 0, "y": 24}}, {"id": 7, "title": "Top Pages", "type": "table", "targets": [{"expr": "topk(10, sum(rate(arca_emr_page_view[5m])) by (url))", "format": "table"}], "gridPos": {"h": 8, "w": 12, "x": 0, "y": 28}}, {"id": 8, "title": "Medical Record Actions", "type": "graph", "targets": [{"expr": "sum(rate(arca_emr_patient_action[5m])) by (action)", "legendFormat": "Patient: {{action}}"}, {"expr": "sum(rate(arca_emr_consultation_action[5m])) by (action)", "legendFormat": "Consultation: {{action}}"}, {"expr": "sum(rate(arca_emr_medical_record_action[5m])) by (action)", "legendFormat": "Record: {{action}}"}], "gridPos": {"h": 8, "w": 12, "x": 12, "y": 28}}]}}