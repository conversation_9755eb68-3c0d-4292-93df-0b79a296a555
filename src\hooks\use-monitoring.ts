/**
 * React Hook for Frontend Monitoring
 * Provides easy-to-use monitoring functions for React components
 */

import { useEffect, useCallback, useRef } from 'react';

import {
  recordMetric,
  recordUserInteraction,
  recordError,
  setUserId,
  metricsCollector,
} from '@/core/lib/monitoring/metrics';

export interface UseMonitoringOptions {
  componentName?: string;
  trackPageViews?: boolean;
  trackUserInteractions?: boolean;
  trackErrors?: boolean;
}

export const useMonitoring = (options: UseMonitoringOptions = {}) => {
  const {
    componentName = 'UnknownComponent',
    trackPageViews = true,
    trackUserInteractions = true,
    trackErrors = true,
  } = options;

  const mountTimeRef = useRef<number>(Date.now());
  const pageViewTrackedRef = useRef<boolean>(false);

  // Track page views
  useEffect(() => {
    if (trackPageViews && !pageViewTrackedRef.current) {
      recordMetric('page_view', 1, {
        component: componentName,
        url: window.location.pathname,
        referrer: document.referrer || 'direct',
      });
      pageViewTrackedRef.current = true;
    }
  }, [componentName, trackPageViews]);

  // Track component mount/unmount
  useEffect(() => {
    const mountTime = mountTimeRef.current;

    recordMetric('component_mount', 1, {
      component: componentName,
      url: window.location.pathname,
    });

    return () => {
      const unmountTime = Date.now();
      const timeOnComponent = unmountTime - mountTime;

      recordMetric('component_unmount', 1, {
        component: componentName,
        time_on_component: timeOnComponent.toString(),
      });

      recordMetric('component_time_spent', timeOnComponent, {
        component: componentName,
        url: window.location.pathname,
      });
    };
  }, [componentName]);

  // Track button clicks
  const trackClick = useCallback(
    (buttonName: string, metadata?: Record<string, any>) => {
      if (!trackUserInteractions) return;

      recordUserInteraction('click', componentName, {
        button: buttonName,
        ...metadata,
      });

      recordMetric('button_click', 1, {
        component: componentName,
        button: buttonName,
        url: window.location.pathname,
      });
    },
    [componentName, trackUserInteractions]
  );

  // Track form submissions
  const trackFormSubmit = useCallback(
    (formName: string, success: boolean, metadata?: Record<string, any>) => {
      if (!trackUserInteractions) return;

      recordUserInteraction('form_submit', componentName, {
        form: formName,
        success,
        ...metadata,
      });

      recordMetric('form_submission', 1, {
        component: componentName,
        form: formName,
        success: success.toString(),
        url: window.location.pathname,
      });
    },
    [componentName, trackUserInteractions]
  );

  // Track modal opens/closes
  const trackModal = useCallback(
    (
      modalName: string,
      action: 'open' | 'close',
      metadata?: Record<string, any>
    ) => {
      if (!trackUserInteractions) return;

      recordUserInteraction(`modal_${action}`, componentName, {
        modal: modalName,
        ...metadata,
      });

      recordMetric(`modal_${action}`, 1, {
        component: componentName,
        modal: modalName,
        url: window.location.pathname,
      });
    },
    [componentName, trackUserInteractions]
  );

  // Track search actions
  const trackSearch = useCallback(
    (query: string, resultsCount: number, metadata?: Record<string, any>) => {
      if (!trackUserInteractions) return;

      recordUserInteraction('search', componentName, {
        query,
        results_count: resultsCount,
        ...metadata,
      });

      recordMetric('search_performed', 1, {
        component: componentName,
        has_results: resultsCount > 0 ? 'true' : 'false',
        url: window.location.pathname,
      });
    },
    [componentName, trackUserInteractions]
  );

  // Track custom events
  const trackEvent = useCallback(
    (eventName: string, value: number = 1, metadata?: Record<string, any>) => {
      recordMetric(eventName, value, {
        component: componentName,
        url: window.location.pathname,
        ...metadata,
      });
    },
    [componentName]
  );

  // Track errors with context
  const trackError = useCallback(
    (error: Error | string, context?: Record<string, any>) => {
      if (!trackErrors) return;

      recordError(error, {
        component: componentName,
        url: window.location.pathname,
        ...context,
      });
    },
    [componentName, trackErrors]
  );

  // Track performance metrics
  const trackPerformance = useCallback(
    (metricName: string, value: number, metadata?: Record<string, any>) => {
      recordMetric(`performance_${metricName}`, value, {
        component: componentName,
        url: window.location.pathname,
        ...metadata,
      });
    },
    [componentName]
  );

  // Track API loading states
  const trackApiLoading = useCallback(
    (apiName: string, isLoading: boolean) => {
      recordMetric('api_loading_state', isLoading ? 1 : 0, {
        component: componentName,
        api: apiName,
        state: isLoading ? 'loading' : 'complete',
        url: window.location.pathname,
      });
    },
    [componentName]
  );

  // Set user ID for session tracking
  const setUserIdForTracking = useCallback((userId: string) => {
    setUserId(userId);
  }, []);

  return {
    // User interaction tracking
    trackClick,
    trackFormSubmit,
    trackModal,
    trackSearch,

    // Custom event tracking
    trackEvent,
    trackError,
    trackPerformance,
    trackApiLoading,

    // User management
    setUserIdForTracking,

    // Direct access to metrics collector for advanced usage
    metricsCollector,
  };
};

// Hook for tracking specific medical/EMR actions
export const useEMRMonitoring = (componentName: string) => {
  const monitoring = useMonitoring({ componentName });

  const trackPatientAction = useCallback(
    (action: string, patientId?: string, metadata?: Record<string, any>) => {
      monitoring.trackEvent('patient_action', 1, {
        action,
        patient_id: patientId,
        ...metadata,
      });
    },
    [monitoring]
  );

  const trackConsultationAction = useCallback(
    (
      action: string,
      consultationId?: string,
      metadata?: Record<string, any>
    ) => {
      monitoring.trackEvent('consultation_action', 1, {
        action,
        consultation_id: consultationId,
        ...metadata,
      });
    },
    [monitoring]
  );

  const trackMedicalRecordAction = useCallback(
    (action: string, recordType: string, metadata?: Record<string, any>) => {
      monitoring.trackEvent('medical_record_action', 1, {
        action,
        record_type: recordType,
        ...metadata,
      });
    },
    [monitoring]
  );

  const trackPrescriptionAction = useCallback(
    (
      action: string,
      prescriptionId?: string,
      metadata?: Record<string, any>
    ) => {
      monitoring.trackEvent('prescription_action', 1, {
        action,
        prescription_id: prescriptionId,
        ...metadata,
      });
    },
    [monitoring]
  );

  return {
    ...monitoring,
    trackPatientAction,
    trackConsultationAction,
    trackMedicalRecordAction,
    trackPrescriptionAction,
  };
};

export default useMonitoring;
