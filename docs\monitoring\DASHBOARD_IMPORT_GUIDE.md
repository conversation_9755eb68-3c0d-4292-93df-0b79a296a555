# Grafana Dashboard Import Guide

This guide explains how to import and configure the pre-built dashboards for ARCA EMR monitoring.

## Available Dashboards

### 1. Main Application Dashboard (`grafana-dashboard-config.json`)
- **Purpose**: Overall application health and performance
- **Key Metrics**: Page views, API response times, error rates, Core Web Vitals
- **Refresh Rate**: 30 seconds
- **Best For**: Real-time monitoring and quick health checks

### 2. Business Metrics Dashboard (`emr-business-metrics-dashboard.json`)
- **Purpose**: Medical workflow and business intelligence
- **Key Metrics**: Patient management, consultations, prescriptions, lab tests
- **Refresh Rate**: 1 minute
- **Best For**: Understanding user behavior and business performance

### 3. Performance & Error Dashboard (`performance-error-dashboard.json`)
- **Purpose**: Technical performance monitoring and error tracking
- **Key Metrics**: API performance, component performance, error analysis
- **Refresh Rate**: 30 seconds
- **Best For**: Debugging performance issues and tracking errors

### 4. User Analytics Dashboard (`user-analytics-dashboard.json`)
- **Purpose**: User behavior analysis and engagement metrics
- **Key Metrics**: User interactions, session analysis, component usage
- **Refresh Rate**: 1 minute
- **Best For**: UX optimization and user journey analysis

## Import Instructions

### Method 1: JSON Import (Recommended)

1. **Access Grafana Cloud**:
   - Log into your Grafana Cloud instance
   - Navigate to **Dashboards** → **Browse**

2. **Import Dashboard**:
   - Click **New** → **Import**
   - Copy the JSON content from any dashboard file
   - Paste into the **Import via panel json** text area
   - Click **Load**

3. **Configure Dashboard**:
   - **Name**: Keep the default or customize
   - **Folder**: Select or create a folder (e.g., "ARCA EMR")
   - **UID**: Leave blank for auto-generation
   - **Data Source**: Select your Prometheus data source
   - Click **Import**

### Method 2: Manual Dashboard Creation

If JSON import doesn't work, create dashboards manually:

1. **Create New Dashboard**:
   - Go to **Dashboards** → **New** → **New Dashboard**
   - Click **Add visualization**

2. **Add Panels** (example for main metrics):

#### Application Overview Panel:
```
Panel Type: Stat
Queries:
- sum(rate(arca_emr_page_view[5m])) (Page Views/min)
- sum(rate(arca_emr_api_call_count[5m])) (API Calls/min)
- sum(rate(arca_emr_frontend_error_count[5m])) (Errors/min)
```

#### API Response Times Panel:
```
Panel Type: Time series
Queries:
- histogram_quantile(0.95, rate(arca_emr_api_call_duration_bucket[5m]))
- histogram_quantile(0.50, rate(arca_emr_api_call_duration_bucket[5m]))
```

## Dashboard Configuration

### Time Range Settings
- **Default**: Last 1 hour for real-time dashboards
- **Business Metrics**: Last 24 hours for trend analysis
- **Adjust as needed** based on your monitoring requirements

### Refresh Intervals
- **Real-time monitoring**: 30 seconds
- **Business analytics**: 1-5 minutes
- **Historical analysis**: 5-15 minutes

### Variable Configuration

Add these dashboard variables for better filtering:

1. **Environment Variable**:
   ```
   Name: environment
   Type: Query
   Query: label_values(arca_emr_page_view, environment)
   ```

2. **Time Range Variable**:
   ```
   Name: timerange
   Type: Interval
   Values: 5m,15m,30m,1h,6h,12h,1d
   ```

3. **Component Filter**:
   ```
   Name: component
   Type: Query
   Query: label_values(arca_emr_user_interaction, component)
   ```

## Customization Tips

### Panel Customization

1. **Colors and Themes**:
   - Use consistent color schemes across dashboards
   - Green for success metrics, red for errors, blue for neutral

2. **Units and Formatting**:
   - Response times: milliseconds (ms)
   - Rates: requests per second (reqps)
   - Percentages: percent (%)
   - Durations: seconds (s) or minutes (m)

3. **Thresholds**:
   - Set meaningful thresholds for your application
   - Example: API response time > 3000ms = red

### Query Optimization

1. **Use appropriate time ranges** in queries:
   - `[5m]` for real-time metrics
   - `[1h]` for trend analysis
   - `[24h]` for daily summaries

2. **Optimize expensive queries**:
   - Use `rate()` instead of `increase()` for rates
   - Use `histogram_quantile()` for percentiles
   - Limit `topk()` results to reasonable numbers

## Alert Configuration

### Recommended Alerts

1. **High Error Rate**:
   ```
   Query: sum(rate(arca_emr_frontend_error_count[5m])) > 0.1
   Condition: IS ABOVE 0.1
   Evaluation: Every 1m for 2m
   ```

2. **Slow API Response**:
   ```
   Query: histogram_quantile(0.95, rate(arca_emr_api_call_duration_bucket[5m])) > 5000
   Condition: IS ABOVE 5000
   Evaluation: Every 1m for 3m
   ```

3. **Low User Activity** (possible outage):
   ```
   Query: sum(rate(arca_emr_page_view[5m])) < 0.01
   Condition: IS BELOW 0.01
   Evaluation: Every 1m for 5m
   ```

## Troubleshooting

### No Data Showing

1. **Check Data Source**:
   - Verify Prometheus data source is configured correctly
   - Test connection in Data Sources settings

2. **Check Time Range**:
   - Ensure time range covers period when data was collected
   - Try "Last 24 hours" to see if any data exists

3. **Verify Metrics**:
   - Go to **Explore** and test individual queries
   - Check if metric names match exactly

### Slow Dashboard Performance

1. **Optimize Queries**:
   - Reduce time ranges where possible
   - Use more specific label filters
   - Limit `topk()` results

2. **Reduce Panel Count**:
   - Split complex dashboards into multiple focused ones
   - Use dashboard links to navigate between related dashboards

3. **Adjust Refresh Rates**:
   - Increase refresh intervals for less critical metrics
   - Use auto-refresh only when necessary

### Query Errors

1. **Syntax Issues**:
   - Check Prometheus query syntax
   - Verify metric names and label names
   - Use **Explore** to test queries

2. **Missing Labels**:
   - Ensure your application is sending the expected labels
   - Check metric collection in browser developer tools

## Best Practices

1. **Dashboard Organization**:
   - Group related dashboards in folders
   - Use consistent naming conventions
   - Add descriptions to panels

2. **Performance**:
   - Don't over-refresh dashboards
   - Use appropriate time ranges
   - Optimize queries for your data volume

3. **Maintenance**:
   - Regularly review and update dashboards
   - Remove unused panels and dashboards
   - Keep dashboard JSON backups

4. **Sharing**:
   - Use dashboard links for navigation
   - Set up proper permissions for team access
   - Document dashboard purposes and usage

## Next Steps

After importing dashboards:

1. **Customize for your needs** - Adjust queries, thresholds, and layouts
2. **Set up alerts** - Configure notifications for critical issues
3. **Train your team** - Ensure team members know how to use the dashboards
4. **Monitor and iterate** - Continuously improve based on actual usage patterns
