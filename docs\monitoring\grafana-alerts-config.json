{"alerts": [{"uid": "arca-emr-high-error-rate", "title": "ARCA EMR - High Frontend Error Rate", "condition": "C", "data": [{"refId": "A", "queryType": "", "relativeTimeRange": {"from": 300, "to": 0}, "model": {"expr": "sum(rate(arca_emr_frontend_error_count[5m])) * 60", "interval": "", "refId": "A"}}, {"refId": "C", "queryType": "", "relativeTimeRange": {"from": 0, "to": 0}, "model": {"conditions": [{"evaluator": {"params": [5], "type": "gt"}, "operator": {"type": "and"}, "query": {"params": ["A"]}, "reducer": {"params": [], "type": "last"}, "type": "query"}], "refId": "C"}}], "intervalSeconds": 60, "maxDataPoints": 43200, "noDataState": "NoData", "execErrState": "Alerting", "for": "2m", "annotations": {"description": "Frontend error rate is above 5 errors per minute. This indicates potential issues with the application.", "runbook_url": "https://your-runbook-url.com/high-error-rate", "summary": "High error rate detected in ARCA EMR frontend"}, "labels": {"severity": "critical", "team": "frontend", "service": "arca-emr"}}, {"uid": "arca-emr-slow-api-response", "title": "ARCA EMR - Slow API Response Times", "condition": "C", "data": [{"refId": "A", "queryType": "", "relativeTimeRange": {"from": 300, "to": 0}, "model": {"expr": "histogram_quantile(0.95, rate(arca_emr_api_call_duration_bucket[5m]))", "interval": "", "refId": "A"}}, {"refId": "C", "queryType": "", "relativeTimeRange": {"from": 0, "to": 0}, "model": {"conditions": [{"evaluator": {"params": [5000], "type": "gt"}, "operator": {"type": "and"}, "query": {"params": ["A"]}, "reducer": {"params": [], "type": "last"}, "type": "query"}], "refId": "C"}}], "intervalSeconds": 60, "maxDataPoints": 43200, "noDataState": "NoData", "execErrState": "Alerting", "for": "3m", "annotations": {"description": "95th percentile API response time is above 5 seconds. Users may experience slow performance.", "runbook_url": "https://your-runbook-url.com/slow-api", "summary": "API response times are degraded"}, "labels": {"severity": "warning", "team": "backend", "service": "arca-emr"}}, {"uid": "arca-emr-low-user-activity", "title": "ARCA EMR - Low User Activity (Possible Outage)", "condition": "C", "data": [{"refId": "A", "queryType": "", "relativeTimeRange": {"from": 300, "to": 0}, "model": {"expr": "sum(rate(arca_emr_page_view[5m]))", "interval": "", "refId": "A"}}, {"refId": "C", "queryType": "", "relativeTimeRange": {"from": 0, "to": 0}, "model": {"conditions": [{"evaluator": {"params": [0.01], "type": "lt"}, "operator": {"type": "and"}, "query": {"params": ["A"]}, "reducer": {"params": [], "type": "last"}, "type": "query"}], "refId": "C"}}], "intervalSeconds": 60, "maxDataPoints": 43200, "noDataState": "Alerting", "execErrState": "Alerting", "for": "5m", "annotations": {"description": "Page view rate is extremely low, indicating possible application outage or accessibility issues.", "runbook_url": "https://your-runbook-url.com/outage-response", "summary": "Possible application outage detected"}, "labels": {"severity": "critical", "team": "sre", "service": "arca-emr"}}, {"uid": "arca-emr-high-api-error-rate", "title": "ARCA EMR - High API Error Rate", "condition": "C", "data": [{"refId": "A", "queryType": "", "relativeTimeRange": {"from": 300, "to": 0}, "model": {"expr": "sum(rate(arca_emr_api_call_count{success=\"false\"}[5m])) / sum(rate(arca_emr_api_call_count[5m])) * 100", "interval": "", "refId": "A"}}, {"refId": "C", "queryType": "", "relativeTimeRange": {"from": 0, "to": 0}, "model": {"conditions": [{"evaluator": {"params": [10], "type": "gt"}, "operator": {"type": "and"}, "query": {"params": ["A"]}, "reducer": {"params": [], "type": "last"}, "type": "query"}], "refId": "C"}}], "intervalSeconds": 60, "maxDataPoints": 43200, "noDataState": "NoData", "execErrState": "Alerting", "for": "2m", "annotations": {"description": "API error rate is above 10%. This indicates backend service issues affecting user experience.", "runbook_url": "https://your-runbook-url.com/api-errors", "summary": "High API error rate detected"}, "labels": {"severity": "critical", "team": "backend", "service": "arca-emr"}}, {"uid": "arca-emr-poor-core-web-vitals", "title": "ARCA EMR - Poor Core Web Vitals", "condition": "C", "data": [{"refId": "A", "queryType": "", "relativeTimeRange": {"from": 600, "to": 0}, "model": {"expr": "avg(arca_emr_performance_largest_contentful_paint)", "interval": "", "refId": "A"}}, {"refId": "C", "queryType": "", "relativeTimeRange": {"from": 0, "to": 0}, "model": {"conditions": [{"evaluator": {"params": [4000], "type": "gt"}, "operator": {"type": "and"}, "query": {"params": ["A"]}, "reducer": {"params": [], "type": "last"}, "type": "query"}], "refId": "C"}}], "intervalSeconds": 300, "maxDataPoints": 43200, "noDataState": "NoData", "execErrState": "Alerting", "for": "10m", "annotations": {"description": "Largest Contentful Paint (LCP) is above 4 seconds, indicating poor user experience.", "runbook_url": "https://your-runbook-url.com/performance-optimization", "summary": "Core Web Vitals performance is degraded"}, "labels": {"severity": "warning", "team": "frontend", "service": "arca-emr"}}, {"uid": "arca-emr-authentication-failures", "title": "ARCA EMR - High Authentication Failure Rate", "condition": "C", "data": [{"refId": "A", "queryType": "", "relativeTimeRange": {"from": 300, "to": 0}, "model": {"expr": "sum(rate(arca_emr_emr_error{error_type=\"authentication\"}[5m])) * 60", "interval": "", "refId": "A"}}, {"refId": "C", "queryType": "", "relativeTimeRange": {"from": 0, "to": 0}, "model": {"conditions": [{"evaluator": {"params": [3], "type": "gt"}, "operator": {"type": "and"}, "query": {"params": ["A"]}, "reducer": {"params": [], "type": "last"}, "type": "query"}], "refId": "C"}}], "intervalSeconds": 60, "maxDataPoints": 43200, "noDataState": "NoData", "execErrState": "Alerting", "for": "2m", "annotations": {"description": "Authentication failure rate is above 3 per minute. This may indicate authentication service issues or security concerns.", "runbook_url": "https://your-runbook-url.com/auth-issues", "summary": "High authentication failure rate"}, "labels": {"severity": "warning", "team": "security", "service": "arca-emr"}}], "contactPoints": [{"uid": "arca-emr-email-alerts", "name": "ARCA EMR Email Alerts", "type": "email", "settings": {"addresses": "<EMAIL>;<EMAIL>", "subject": "ARCA EMR Alert: {{ .GroupLabels.alertname }}", "message": "Alert: {{ .GroupLabels.alertname }}\nSeverity: {{ .GroupLabels.severity }}\nDescription: {{ .CommonAnnotations.description }}\nRunbook: {{ .CommonAnnotations.runbook_url }}"}}, {"uid": "arca-emr-slack-alerts", "name": "ARCA EMR Slack Alerts", "type": "slack", "settings": {"url": "YOUR_SLACK_WEBHOOK_URL", "channel": "#arca-emr-alerts", "username": "<PERSON><PERSON>", "title": "ARCA EMR Alert: {{ .GroupLabels.alertname }}", "text": "{{ .CommonAnnotations.summary }}\n*Severity:* {{ .GroupLabels.severity }}\n*Description:* {{ .CommonAnnotations.description }}\n*Runbook:* {{ .CommonAnnotations.runbook_url }}"}}], "notificationPolicies": [{"receiver": "arca-emr-email-alerts", "group_by": ["alertname", "severity"], "group_wait": "30s", "group_interval": "5m", "repeat_interval": "1h", "matchers": [{"name": "service", "value": "arca-emr", "isRegex": false}], "routes": [{"receiver": "arca-emr-slack-alerts", "group_wait": "10s", "group_interval": "2m", "repeat_interval": "30m", "matchers": [{"name": "severity", "value": "critical", "isRegex": false}]}]}]}