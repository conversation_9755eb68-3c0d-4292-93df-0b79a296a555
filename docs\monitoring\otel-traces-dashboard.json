{"dashboard": {"id": null, "title": "ARCA EMR - OpenTelemetry Traces", "tags": ["arca-emr", "opentelemetry", "traces"], "timezone": "", "panels": [{"id": 1, "title": "Trace Overview", "type": "stat", "targets": [{"expr": "sum(rate(traces_total[5m]))", "refId": "A"}], "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"displayMode": "list", "orientation": "horizontal"}, "mappings": [], "thresholds": {"steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 0}}, {"id": 2, "title": "API Call Traces", "type": "table", "targets": [{"expr": "topk(10, sum by (operation_name) (rate(traces_total{operation_name=~\"HTTP.*\"}[5m])))", "refId": "A"}], "fieldConfig": {"defaults": {"custom": {"align": "auto", "displayMode": "list", "inspect": false}, "mappings": [], "thresholds": {"steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 0}}, {"id": 3, "title": "Trace Duration Distribution", "type": "heatmap", "targets": [{"expr": "sum(rate(trace_duration_bucket[5m])) by (le)", "refId": "A"}], "fieldConfig": {"defaults": {"custom": {"hideFrom": {"legend": false, "tooltip": false, "vis": false}, "scaleDistribution": {"type": "linear"}}}}, "gridPos": {"h": 8, "w": 24, "x": 0, "y": 8}}, {"id": 4, "title": "Error Traces", "type": "table", "targets": [{"expr": "topk(10, sum by (operation_name, error_message) (rate(traces_total{status=\"error\"}[5m])))", "refId": "A"}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "auto", "displayMode": "list", "inspect": false}, "mappings": [], "thresholds": {"steps": [{"color": "green", "value": null}, {"color": "red", "value": 1}]}}}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 16}}, {"id": 5, "title": "User Interaction Traces", "type": "timeseries", "targets": [{"expr": "sum by (user_action) (rate(traces_total{operation_name=~\"User.*\"}[5m]))", "refId": "A"}], "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "vis": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 16}}, {"id": 6, "title": "Service Map", "type": "nodeGraph", "targets": [{"expr": "sum by (service_name, operation_name) (rate(traces_total[5m]))", "refId": "A"}], "fieldConfig": {"defaults": {"custom": {"hideFrom": {"legend": false, "tooltip": false, "vis": false}}}}, "gridPos": {"h": 8, "w": 24, "x": 0, "y": 24}}], "time": {"from": "now-1h", "to": "now"}, "timepicker": {}, "uid": "arca-emr-otel-traces", "version": 1, "weekStart": ""}}