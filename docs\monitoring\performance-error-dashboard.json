{"dashboard": {"id": null, "title": "ARCA EMR - Performance & Error Tracking", "tags": ["arca-emr", "performance", "errors", "qa"], "timezone": "browser", "refresh": "30s", "time": {"from": "now-1h", "to": "now"}, "panels": [{"id": 1, "title": "Error Rate Overview", "type": "stat", "targets": [{"expr": "sum(rate(arca_emr_frontend_error_count[5m])) * 60", "legendFormat": "Frontend Errors/min"}, {"expr": "sum(rate(arca_emr_api_call_count{success=\"false\"}[5m])) * 60", "legendFormat": "API Errors/min"}, {"expr": "sum(rate(arca_emr_emr_error[5m])) * 60", "legendFormat": "EMR Errors/min"}], "gridPos": {"h": 6, "w": 24, "x": 0, "y": 0}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "unit": "short", "thresholds": {"steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 1}, {"color": "red", "value": 5}]}}}}, {"id": 2, "title": "API Response Times", "type": "timeseries", "targets": [{"expr": "histogram_quantile(0.95, rate(arca_emr_api_call_duration_bucket[5m]))", "legendFormat": "95th percentile"}, {"expr": "histogram_quantile(0.75, rate(arca_emr_api_call_duration_bucket[5m]))", "legendFormat": "75th percentile"}, {"expr": "histogram_quantile(0.50, rate(arca_emr_api_call_duration_bucket[5m]))", "legendFormat": "50th percentile"}], "gridPos": {"h": 8, "w": 12, "x": 0, "y": 6}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "unit": "ms", "thresholds": {"steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 1000}, {"color": "red", "value": 3000}]}}}}, {"id": 3, "title": "Core Web Vitals", "type": "timeseries", "targets": [{"expr": "avg(arca_emr_performance_largest_contentful_paint)", "legendFormat": "LCP (ms)"}, {"expr": "avg(arca_emr_performance_first_input_delay)", "legendFormat": "FID (ms)"}, {"expr": "avg(arca_emr_performance_cumulative_layout_shift) * 1000", "legendFormat": "CLS (x1000)"}], "gridPos": {"h": 8, "w": 12, "x": 12, "y": 6}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "unit": "ms"}}}, {"id": 4, "title": "Error Rate by API Endpoint", "type": "timeseries", "targets": [{"expr": "sum(rate(arca_emr_api_call_count{success=\"false\"}[5m])) by (endpoint)", "legendFormat": "{{endpoint}}"}], "gridPos": {"h": 8, "w": 12, "x": 0, "y": 14}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "unit": "reqps"}}}, {"id": 5, "title": "Component Performance", "type": "timeseries", "targets": [{"expr": "avg(arca_emr_component_time_spent) by (component_name) / 1000", "legendFormat": "{{component_name}}"}], "gridPos": {"h": 8, "w": 12, "x": 12, "y": 14}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "unit": "s"}}}, {"id": 6, "title": "EMR Operation Performance", "type": "timeseries", "targets": [{"expr": "avg(arca_emr_emr_performance) by (operation)", "legendFormat": "{{operation}}"}], "gridPos": {"h": 8, "w": 12, "x": 0, "y": 22}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "unit": "ms"}}}, {"id": 7, "title": "<PERSON> Load Performance", "type": "timeseries", "targets": [{"expr": "avg(arca_emr_performance_page_load_time)", "legendFormat": "Page Load Time"}, {"expr": "avg(arca_emr_performance_dom_content_loaded)", "legendFormat": "DOM Content Loaded"}], "gridPos": {"h": 8, "w": 12, "x": 12, "y": 22}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "unit": "ms"}}}, {"id": 8, "title": "Top Error Messages (Last Hour)", "type": "table", "targets": [{"expr": "topk(10, sum(increase(arca_emr_frontend_error_count[1h])) by (error_type))", "format": "table", "instant": true}], "gridPos": {"h": 8, "w": 12, "x": 0, "y": 30}, "transformations": [{"id": "organize", "options": {"excludeByName": {"Time": true}, "renameByName": {"error_type": "Error Type", "Value": "Count"}}}]}, {"id": 9, "title": "Slowest API Endpoints", "type": "table", "targets": [{"expr": "topk(10, avg(arca_emr_api_call_duration) by (endpoint))", "format": "table", "instant": true}], "gridPos": {"h": 8, "w": 12, "x": 12, "y": 30}, "transformations": [{"id": "organize", "options": {"excludeByName": {"Time": true}, "renameByName": {"endpoint": "Endpoint", "Value": "Avg Response Time (ms)"}}}]}, {"id": 10, "title": "Browser Performance Distribution", "type": "heatmap", "targets": [{"expr": "sum(rate(arca_emr_performance_page_load_time_bucket[5m])) by (le)", "format": "heatmap", "legendFormat": "{{le}}"}], "gridPos": {"h": 8, "w": 24, "x": 0, "y": 38}, "fieldConfig": {"defaults": {"color": {"mode": "spectrum"}, "unit": "ms"}}}, {"id": 11, "title": "Memory Usage Trends", "type": "timeseries", "targets": [{"expr": "avg(arca_emr_memory_usage)", "legendFormat": "Memory Usage (MB)"}], "gridPos": {"h": 6, "w": 12, "x": 0, "y": 46}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "unit": "decbytes"}}}, {"id": 12, "title": "Network Request Success Rate", "type": "stat", "targets": [{"expr": "sum(rate(arca_emr_api_call_count{success=\"true\"}[5m])) / sum(rate(arca_emr_api_call_count[5m])) * 100", "legendFormat": "Success Rate %"}], "gridPos": {"h": 6, "w": 12, "x": 12, "y": 46}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "unit": "percent", "min": 0, "max": 100, "thresholds": {"steps": [{"color": "red", "value": null}, {"color": "yellow", "value": 95}, {"color": "green", "value": 99}]}}}}]}}