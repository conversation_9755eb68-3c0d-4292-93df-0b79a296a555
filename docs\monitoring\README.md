# ARCA EMR Monitoring System

A comprehensive monitoring solution for the ARCA EMR application using Grafana Cloud, designed specifically for the QA environment deployed on Vercel.

## Overview

This monitoring system provides:
- **Real-time application monitoring** with custom metrics
- **Performance tracking** including Core Web Vitals
- **Error tracking and alerting** for proactive issue resolution
- **User behavior analytics** for UX optimization
- **Business metrics** specific to EMR workflows
- **Automated alerting** with email and Slack notifications

## Architecture

```
Frontend (Next.js) → Metrics Collection → API Endpoint → Grafana Cloud
                                                      ↓
                                              Dashboards & Alerts
```

### Components

1. **Frontend Metrics Collection** (`src/core/lib/monitoring/`)
   - Custom metrics for business logic
   - Performance monitoring (Core Web Vitals)
   - Error tracking with context
   - User interaction analytics

2. **API Integration** (`src/core/lib/interceptor/`)
   - Automatic API call monitoring
   - Response time tracking
   - Error rate monitoring

3. **Metrics API** (`src/app/api/metrics/`)
   - Collects frontend metrics
   - Formats data for Grafana Cloud
   - Handles both metrics and logs

4. **Grafana Cloud Integration**
   - Prometheus for metrics storage
   - Loki for log aggregation
   - Pre-built dashboards
   - Automated alerting

## Quick Start

### 1. Environment Setup

Add these environment variables to your Vercel project:

```bash
NEXT_PUBLIC_METRICS_ENDPOINT=https://arca-emr.vercel.app/api
GRAFANA_CLOUD_URL=https://prometheus-prod-XX-XX-X.grafana.net
GRAFANA_CLOUD_USER=your-user-id
GRAFANA_CLOUD_API_KEY=your-api-key
```

### 2. Grafana Cloud Setup

1. Sign up for Grafana Cloud (free tier available)
2. Get your Prometheus and Loki endpoints
3. Create API key with metrics:write and logs:write permissions
4. Update environment variables with your credentials

### 3. Deploy and Test

1. Deploy your application to Vercel
2. Visit your application to generate metrics
3. Check Grafana Cloud for incoming data
4. Import the provided dashboards

## Documentation

### Setup Guides
- **[Grafana Setup Guide](GRAFANA_SETUP_GUIDE.md)** - Complete Grafana Cloud configuration
- **[Deployment & Testing Guide](DEPLOYMENT_TESTING_GUIDE.md)** - Deploy and validate monitoring
- **[Alerting Setup Guide](ALERTING_SETUP_GUIDE.md)** - Configure alerts and notifications

### Dashboard Guides
- **[Dashboard Import Guide](DASHBOARD_IMPORT_GUIDE.md)** - Import and configure dashboards
- **[Component Integration Examples](COMPONENT_INTEGRATION_EXAMPLES.md)** - Add monitoring to React components

### Configuration Files
- **[Main Dashboard](grafana-dashboard-config.json)** - Application overview dashboard
- **[Business Metrics Dashboard](emr-business-metrics-dashboard.json)** - EMR-specific metrics
- **[Performance Dashboard](performance-error-dashboard.json)** - Performance and error tracking
- **[User Analytics Dashboard](user-analytics-dashboard.json)** - User behavior analysis
- **[Alert Configuration](grafana-alerts-config.json)** - Pre-configured alerts

## Key Features

### Metrics Collected

#### Application Metrics
- Page views and navigation patterns
- User sessions and engagement
- Component render times and interactions
- Form submissions and success rates

#### Performance Metrics
- Core Web Vitals (LCP, FID, CLS)
- API response times (percentiles)
- Page load performance
- Component performance

#### Business Metrics (EMR-Specific)
- Patient management actions
- Consultation workflows
- Prescription activities
- Lab test orders and results
- Lifestyle monitoring usage
- Ambient listening sessions

#### Error Tracking
- JavaScript errors with stack traces
- API failures with context
- Authentication issues
- Component-specific errors

### Dashboards Available

1. **Application Overview**
   - Real-time health monitoring
   - Key performance indicators
   - Error rate tracking

2. **Business Metrics**
   - Medical workflow analytics
   - User activity patterns
   - Feature usage statistics

3. **Performance & Errors**
   - Technical performance monitoring
   - Error analysis and trends
   - Performance bottleneck identification

4. **User Analytics**
   - User behavior analysis
   - Engagement metrics
   - Journey flow analysis

### Alerting

Pre-configured alerts for:
- High error rates (>5 errors/minute)
- Slow API responses (>5 seconds)
- Application outages (low activity)
- Poor performance (Core Web Vitals)
- Authentication failures

## Usage Examples

### Basic Component Monitoring

```typescript
import { useMonitoring } from '@/hooks/use-monitoring';

const MyComponent = () => {
  const { trackClick, trackError } = useMonitoring({
    componentName: 'MyComponent'
  });

  const handleButtonClick = () => {
    trackClick('submit_button');
    // Your logic here
  };

  return <button onClick={handleButtonClick}>Submit</button>;
};
```

### EMR-Specific Tracking

```typescript
import { trackPatientView, trackConsultationStart } from '@/core/lib/monitoring/emr-metrics';

// Track patient interactions
trackPatientView(patientId, 'details');

// Track consultation workflow
trackConsultationStart(consultationId, 'new');
```

### Custom Metrics

```typescript
import { recordMetric } from '@/core/lib/monitoring/metrics';

// Track custom business events
recordMetric('prescription_printed', 1, {
  patient_id: patientId,
  prescription_type: 'chronic'
});
```

## Monitoring Best Practices

### What to Monitor
- **User-facing errors** that affect functionality
- **Performance degradation** that impacts user experience
- **Business-critical workflows** like patient management
- **Security events** like authentication failures

### What NOT to Monitor
- Debug information in production
- Personally identifiable information (PII)
- Excessive detail that creates noise
- Non-actionable metrics

### Performance Considerations
- Metrics are batched and sent every 30 seconds
- Minimal performance impact (<1ms per interaction)
- Automatic cleanup of old data
- Configurable sampling for high-volume events

## Troubleshooting

### Common Issues

1. **No metrics appearing**:
   - Check environment variables
   - Verify Grafana Cloud credentials
   - Test API endpoint directly

2. **Dashboard shows no data**:
   - Check time range settings
   - Verify data source configuration
   - Test queries in Grafana Explore

3. **Alerts not firing**:
   - Verify alert query syntax
   - Check evaluation conditions
   - Test contact point configuration

### Getting Help

- Check the troubleshooting sections in each guide
- Test individual components using browser developer tools
- Use Grafana's Explore feature to debug queries
- Review Vercel function logs for API issues

## Maintenance

### Regular Tasks
- **Weekly**: Review alert effectiveness
- **Monthly**: Analyze performance trends
- **Quarterly**: Update monitoring strategy

### Updates
- Monitor Grafana Cloud feature updates
- Update dashboard configurations as needed
- Adjust alert thresholds based on operational patterns

## Support

For issues or questions:
1. Check the documentation in this folder
2. Review Grafana Cloud documentation
3. Test individual components systematically
4. Check browser console and network tabs for client-side issues

## Contributing

When adding new monitoring:
1. Follow the established patterns in existing code
2. Add appropriate documentation
3. Test thoroughly in development
4. Update relevant dashboards and alerts

---

**Note**: This monitoring system is designed for the QA environment. For production deployment, review and adjust thresholds, retention periods, and alert configurations based on production traffic patterns.
